{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES2020"], "jsx": "preserve", "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/renderer/src/*"], "@components/*": ["src/renderer/src/components/*"], "@pages/*": ["src/renderer/src/pages/*"], "@hooks/*": ["src/renderer/src/hooks/*"], "@utils/*": ["src/renderer/src/utils/*"], "@styles/*": ["src/renderer/src/styles/*"], "@assets/*": ["src/renderer/public/*"], "@main/*": ["src/main/*"], "@common/*": ["src/common/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/renderer/src/**/*.ts", "src/renderer/src/**/*.tsx", "src/renderer/next-env.d.ts"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts", "**/__tests__/**", "**/__mocks__/**"]}