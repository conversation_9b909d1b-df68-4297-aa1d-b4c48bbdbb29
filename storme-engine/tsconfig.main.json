{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "dist/main", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "noEmitOnError": true, "types": ["node", "electron"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "**/*.spec.ts"]}