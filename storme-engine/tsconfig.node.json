{"compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "noImplicitReturns": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "esModuleInterop": true, "isolatedModules": true, "resolveJsonModule": true, "jsx": "preserve", "lib": ["ES2020"], "target": "ES2020", "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/*"], "@common/*": ["src/common/*"]}}, "include": ["src/main/**/*.ts", "src/main/**/*.tsx", "src/common/**/*.ts", "src/common/**/*.tsx", "electron.vite.config.ts"], "exclude": ["node_modules", "dist"]}