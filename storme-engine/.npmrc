# Ensure consistent package-lock.json format
package-lock=true
package-lock-only=false

# Use the latest npm
engine-strict=true

# Prefer exact versions
save-exact=true

# Use package-lock.json
package-lock=true

# Disable package-lock.json for workspaces
workspaces-update=false

# Set the registry (uncomment and set if using a private registry)
# registry=https://registry.npmjs.org/

# Configure for CI/CD
prefer-offline=true
prefer-online=false

# Configure for monorepo
workspaces-prefix=optional

# Configure for Electron
# electron-mirror="https://npmmirror.com/mirrors/electron/"
# electron-custom-dir="{{ version }}"
# electron-custom-debug="{{ version }}"

# Configure for node-gyp
# python=/path/to/executable/python3

# Configure for Git-based dependencies
# git-tag-version=true
# commit-hooks=true

# Configure for security
# audit-level=moderate
# fund=false
# ignore-scripts=false
# strict-ssl=true

# Configure for network
# fetch-retries=2
# fetch-retry-mintimeout=20000
# fetch-retry-maxtimeout=120000

# Configure for logs
# loglevel=notice
# progress=true
# color=true

# Configure for cache
# cache=/path/to/npm-cache
# cache-lock-stale=60000
# cache-lock-retries=10
# cache-lock-wait=10000
# cache-max=0
# cache-min=10

# Configure for tmp
# tmp=/tmp
# user-agent=npm/{npm-version} node/{node-version} {platform} {arch} {ci}

# Configure for ssl
# cafile=/path/to/certificate.pem
# strict-ssl=true

# Configure for proxy
# proxy=http://proxy.example.com:8080/
# https-proxy=http://proxy.example.com:8080/
# noproxy=*.example.com,*.example.org,localhost,127.0.0.1,::1

# Configure for auth
# _auth=username:password
# always-auth=true
# email=<EMAIL>
# username=your-username

# Configure for publish
# access=public
# tag=latest
# otp=123456

# Configure for scripts
# ignore-scripts=false
# script-shell=/bin/bash
# unsafe-perm=true

# Configure for workspaces
# workspace=*
# workspaces-prefix=optional
# workspaces-update=false

# Configure for package manager
# engine-strict=true
# node-version=18.0.0
# npm-version=9.0.0

# Configure for git
# git-tag-version=true
# commit-hooks=true
# tag-version-prefix="v"

# Configure for lifecycle scripts
# ignore-scripts=false
# unsafe-perm=true

# Configure for package distribution
# dist-tag=latest
# tag-version-prefix="v"
# tag-version-prefix=""

# Configure for package signing
# sign-git-tag=false
# sign-git-commit=false
# otp=123456

# Configure for package access
# access=public
# publish-config={"access":"public"}
# publishConfig={"access":"public"}

# Configure for package lockfile
# package-lock=true
# package-lock-only=false
# shrinkwrap=true

# Configure for package installation
# save=true
# save-dev=true
# save-optional=true
# save-prefix=^
# save-exact=true
# save-bundle=false

# Configure for package resolution
# prefer-offline=true
# prefer-online=false
# offline=false

# Configure for package verification
# audit=true
# audit-level=low
# dry-run=false
# json=false

# Configure for package cleanup
# fund=false
# global-style=false
# legacy-bundling=false
# no-package-lock=false
# no-shrinkwrap=false
# no-optional=false
# no-audit=false
# no-fund=false
# no-package-lock=false
# no-shrinkwrap=false
# no-optional=false
# no-audit=false
# no-fund=false

# Configure for package installation location
# prefix=/usr/local
# global-folder=/path/to/global/folder
# global-style=false
# global=true

# Configure for package scripts
# ignore-scripts=false
# script-shell=/bin/bash
# unsafe-perm=true

# Configure for package execution
# node-options=--max-old-space-size=4096
# node-options=--inspect
# node-options=--inspect-brk

# Configure for package debugging
# debug=false
# verbose=false
# silent=false
# quiet=false
# loglevel=notice
# loglevel=error
# loglevel=warn
# loglevel=http
# loglevel=info
# loglevel=verbose
# loglevel=silly

# Configure for package cache
# cache=/path/to/npm-cache
# cache-lock-stale=60000
# cache-lock-retries=10
# cache-lock-wait=10000
# cache-max=0
# cache-min=10

# Configure for package registry
# registry=https://registry.npmjs.org/
# scope=@your-scope
# @your-scope:registry=https://your-private-registry.example.com/

# Configure for package auth
# //registry.npmjs.org/:_authToken=${NPM_TOKEN}
# @your-scope:registry=https://your-private-registry.example.com/
# //your-private-registry.example.com/:_authToken=${NPM_TOKEN}

# Configure for package publishing
# access=public
# tag=latest
# otp=123456

# Configure for package workspaces
# workspaces-prefix=optional
# workspaces-update=false
# workspace=*
# workspace-concurrency=4

# Configure for package execution
# node-options=--max-old-space-size=4096
# node-options=--inspect
# node-options=--inspect-brk

# Configure for package debugging
# debug=false
# verbose=false
# silent=false
# quiet=false
# loglevel=notice
# loglevel=error
# loglevel=warn
# loglevel=http
# loglevel=info
# loglevel=verbose
# loglevel=silly
