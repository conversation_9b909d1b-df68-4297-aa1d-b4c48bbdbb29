# Copy this file to .env and update the values as needed

# Application
NODE_ENV=development
VITE_APP_NAME="Storme Engine"
VITE_APP_VERSION=0.1.0
VITE_APP_HOMEPAGE=https://gamestorme.com
VITE_APP_DEBUG=true

# API Configuration
VITE_API_BASE_URL=http://localhost:4000/api
VITE_WS_BASE_URL=ws://localhost:4000

# Electron
ELECTRON_START_URL=http://localhost:3000
ELECTRON_IS_DEV=true

# Authentication
VITE_AUTH0_DOMAIN=your-auth0-domain.auth0.com
VITE_AUTH0_CLIENT_ID=your-auth0-client-id
VITE_AUTH0_AUDIENCE=your-auth0-audience
VITE_AUTH0_CALLBACK_URL=http://localhost:3000/callback

# Analytics (optional)
VITE_GOOGLE_ANALYTICS_ID=UA-XXXXXXXXX-X
VITE_SENTRY_DSN=your-sentry-dsn

# Feature Flags
VITE_FEATURE_UPDATES=true
VITE_FEATURE_ANALYTICS=false
VITE_FEATURE_OFFLINE_MODE=true

# Development Server
VITE_DEV_SERVER_PORT=3000
VITE_DEV_SERVER_OPEN_BROWSER=false
VITE_DEV_SERVER_HTTPS=false

# Build Configuration
VITE_SOURCE_MAP=true
VITE_DROP_CONSOLE=false
VITE_DROP_DEBUGGER=false

# Performance
VITE_COMPRESSION=true
VITE_CHUNK_SIZE_WARNING_LIMIT=1000
