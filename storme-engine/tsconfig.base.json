{"compilerOptions": {"target": "es2020", "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "composite": true, "declaration": true, "declarationMap": true, "baseUrl": ".", "paths": {"@common/*": ["src/common/*"], "@main/*": ["src/main/*"], "@preload/*": ["src/preload/*"], "@renderer/*": ["src/renderer/src/*"]}}, "exclude": ["node_modules", "dist"]}