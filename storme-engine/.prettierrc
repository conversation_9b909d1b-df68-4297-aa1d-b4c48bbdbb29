{"semi": true, "singleQuote": true, "tabWidth": 2, "useTabs": false, "printWidth": 100, "trailingComma": "es5", "bracketSpacing": true, "jsxSingleQuote": true, "arrowParens": "avoid", "endOfLine": "lf", "overrides": [{"files": "*.json", "options": {"parser": "json", "tabWidth": 2}}, {"files": ["*.yaml", "*.yml"], "options": {"singleQuote": false, "tabWidth": 2}}, {"files": ["*.md", "*.mdx"], "options": {"proseWrap": "always", "printWidth": 80}}]}