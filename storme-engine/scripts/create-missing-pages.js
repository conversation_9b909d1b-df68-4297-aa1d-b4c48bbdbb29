const fs = require('fs');
const path = require('path');

const pagesDir = path.join(__dirname, '../src/renderer/src/pages');
const pages = [
  'DashboardPage',
  'GamesPage',
  'GameDetailsPage',
  'developer/ProjectsPage',
  'developer/AnalyticsPage',
  'developer/AIMarketingPage',
  'developer/MonetizationPage',
  'SettingsPage',
  'ProfilePage',
  'auth/LoginPage',
  'auth/RegisterPage',
  'auth/ForgotPasswordPage',
  'NotFoundPage'
];

// Create pages directory if it doesn't exist
if (!fs.existsSync(pagesDir)) {
  fs.mkdirSync(pagesDir, { recursive: true });
}

// Create developer directory
const developerDir = path.join(pagesDir, 'developer');
if (!fs.existsSync(developerDir)) {
  fs.mkdirSync(developerDir, { recursive: true });
}

// Create auth directory
const authDir = path.join(pagesDir, 'auth');
if (!fs.existsSync(authDir)) {
  fs.mkdirSync(authDir, { recursive: true });
}

// Create page components
pages.forEach(page => {
  const pageName = page.split('/').pop();
  const dir = path.join(pagesDir, path.dirname(page));
  const filePath = path.join(dir, `${pageName}.tsx`);
  
  // Skip if file already exists
  if (fs.existsSync(filePath)) {
    return;
  }
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // Create basic page component
  const componentContent = `import React from 'react';
import { Box, Typography } from '@mui/material';

export const ${pageName}: React.FC = () => {
  return (
    <Box p={3}>
      <Typography variant="h4" component="h1" gutterBottom>
        ${pageName.replace(/([A-Z])/g, ' $1').trim()}
      </Typography>
      <Typography variant="body1">
        ${pageName.replace(/([A-Z])/g, ' $1').trim()} content coming soon!
      </Typography>
    </Box>
  );
};

export default ${pageName};`;
  
  fs.writeFileSync(filePath, componentContent);
  console.log(`Created ${filePath}`);
});

console.log('All missing pages have been created!');
