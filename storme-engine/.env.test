# Test Environment Variables
# This file overrides .env in test mode

# Application
NODE_ENV=test
VITE_APP_DEBUG=false

# API Configuration - Use mock API for testing
VITE_API_BASE_URL=http://localhost:4001/api
VITE_WS_BASE_URL=ws://localhost:4001

# Electron
ELECTRON_IS_DEV=false

# Testing
VITEST=true

# Build Configuration
VITE_SOURCE_MAP=true
VITE_DROP_CONSOLE=false
VITE_DROP_DEBUGGER=false

# Feature Flags
VITE_FEATURE_UPDATES=false
VITE_FEATURE_ANALYTICS=false
VITE_FEATURE_OFFLINE_MODE=true

# Performance
VITE_COMPRESSION=false

# Testing
VITE_TEST_MODE=true
VITE_TEST_USER_EMAIL=<EMAIL>
VITE_TEST_USER_PASSWORD=test1234
