#!/usr/bin/env node

// Test script for Storme Engine Backend
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testBackend() {
  console.log('🧪 Testing Storme Engine Backend...\n');

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health check passed:', healthResponse.data.status);

    // Test 2: Get Games
    console.log('\n2️⃣ Testing games endpoint...');
    const gamesResponse = await axios.get(`${API_BASE}/games`);
    console.log('✅ Games endpoint passed:', `${gamesResponse.data.data.length} games loaded`);

    // Test 3: Register User
    console.log('\n3️⃣ Testing user registration...');
    const testUser = {
      email: `test${Date.now()}@example.com`,
      password: 'testpassword123',
      displayName: 'Test User',
      userType: 'gamer'
    };

    const registerResponse = await axios.post(`${API_BASE}/auth/register`, testUser);
    console.log('✅ User registration passed:', registerResponse.data.user.displayName);
    const token = registerResponse.data.token;

    // Test 4: Login
    console.log('\n4️⃣ Testing user login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ User login passed:', loginResponse.data.user.displayName);

    // Test 5: Get User Dashboard
    console.log('\n5️⃣ Testing user dashboard...');
    const dashboardResponse = await axios.get(`${API_BASE}/user/dashboard`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Dashboard endpoint passed:', `Level ${dashboardResponse.data.data.stats.level} user`);

    // Test 6: Game Operations
    console.log('\n6️⃣ Testing game operations...');
    const gameId = 'cyber-legends';
    
    // Install game
    const installResponse = await axios.post(`${API_BASE}/games/${gameId}/install`, {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Game install passed:', installResponse.data.message);

    // Toggle favorite
    const favoriteResponse = await axios.post(`${API_BASE}/games/${gameId}/favorite`, {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Game favorite passed:', favoriteResponse.data.message);

    console.log('\n🎉 All backend tests passed successfully!');
    console.log('\n📊 Backend Summary:');
    console.log(`   🔗 API Base: ${API_BASE}`);
    console.log(`   🎮 Games Available: ${gamesResponse.data.data.length}`);
    console.log(`   👤 Test User: ${testUser.displayName} (${testUser.userType})`);
    console.log(`   🔐 Authentication: Working`);
    console.log(`   📱 Dashboard: Working`);
    console.log(`   🎯 Game Operations: Working`);

  } catch (error) {
    console.error('\n❌ Backend test failed:');
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Error: ${error.response.data.error || error.response.data.message}`);
    } else if (error.request) {
      console.error('   Network error - is the backend running on port 3001?');
    } else {
      console.error(`   Error: ${error.message}`);
    }
    process.exit(1);
  }
}

// Run tests
testBackend();
