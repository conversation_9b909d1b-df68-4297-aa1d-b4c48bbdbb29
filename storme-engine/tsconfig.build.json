{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": ".", "sourceMap": false, "declaration": true, "declarationMap": false, "removeComments": true, "noEmitOnError": true, "incremental": true, "tsBuildInfoFile": "./node_modules/.cache/tsbuildinfo.json"}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts", "**/__tests__/**", "**/__mocks__/**", "**/test/**", "**/tests/**", "**/*.d.ts"]}