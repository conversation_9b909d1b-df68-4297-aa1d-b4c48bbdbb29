# Development Environment Variables
# This file overrides .env in development mode

# Application
NODE_ENV=development
VITE_APP_DEBUG=true

# API Configuration
VITE_API_BASE_URL=http://localhost:4000/api
VITE_WS_BASE_URL=ws://localhost:4000

# Electron
ELECTRON_IS_DEV=true

# Development Server
VITE_DEV_SERVER_PORT=3000
VITE_DEV_SERVER_OPEN_BROWSER=false
VITE_DEV_SERVER_HTTPS=false

# Build Configuration
VITE_SOURCE_MAP=true
VITE_DROP_CONSOLE=false
VITE_DROP_DEBUGGER=false

# Feature Flags
VITE_FEATURE_UPDATES=true
VITE_FEATURE_ANALYTICS=false
VITE_FEATURE_OFFLINE_MODE=true

# Performance
VITE_COMPRESSION=false
