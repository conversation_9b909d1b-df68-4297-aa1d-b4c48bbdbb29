# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist
/out

# Misc
.DS_Store
*.pem
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.cache/
.temp/

# Electron
release/
app/dist/
app/*.dmg
app/*.exe
app/*.AppImage
app/*.dmg
app/*.zip

# IDE
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Next.js
.next/

# Renderer
src/renderer/.next/
src/renderer/out/
