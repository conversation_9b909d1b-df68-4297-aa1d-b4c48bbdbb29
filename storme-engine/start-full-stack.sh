#!/bin/bash

# Full Stack Storme Engine Startup Script
echo "🚀 Starting Storme Engine Full Stack..."

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    echo "🔄 Killing process on port $port..."
    lsof -ti:$port | xargs kill -9 2>/dev/null || true
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing backend dependencies..."
cd backend
if [ ! -d "node_modules" ]; then
    npm install
fi

# Check if backend port is in use
if check_port 3001; then
    echo "⚠️  Port 3001 is already in use. Killing existing process..."
    kill_port 3001
    sleep 2
fi

echo "🔧 Starting backend server..."
npm start &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 3

# Check if backend is running
if check_port 3001; then
    echo "✅ Backend server started on port 3001"
else
    echo "❌ Failed to start backend server"
    exit 1
fi

# Go back to main directory
cd ..

echo "📦 Installing frontend dependencies..."
if [ ! -d "node_modules" ]; then
    npm install
fi

# Check if frontend port is in use
if check_port 8000; then
    echo "⚠️  Port 8000 is already in use. Killing existing process..."
    kill_port 8000
    sleep 2
fi

echo "🎮 Starting Electron app..."
npm run dev &
FRONTEND_PID=$!

echo "✅ Full stack started successfully!"
echo "🔗 Backend API: http://localhost:3001"
echo "🎮 Electron App: Starting..."
echo ""
echo "📋 Process IDs:"
echo "   Backend: $BACKEND_PID"
echo "   Frontend: $FRONTEND_PID"
echo ""
echo "🛑 To stop all services, press Ctrl+C or run:"
echo "   kill $BACKEND_PID $FRONTEND_PID"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    echo "✅ Services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for processes
wait
