#!/bin/bash

# Build script for Storme Engine
echo "🚀 Building Storme Engine for macOS..."

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script is designed for macOS. For other platforms, use:"
    echo "   Windows: npm run dist:win"
    echo "   Linux: npm run dist:linux"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "🔨 Building the application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build the application"
    exit 1
fi

echo "📱 Creating macOS distribution..."
npm run dist:mac

if [ $? -ne 0 ]; then
    echo "❌ Failed to create macOS distribution"
    exit 1
fi

echo "✅ Build complete!"
echo "📁 Your macOS app is ready in the 'release' folder"
echo "🎮 You can now install and run Storme Engine on your Mac!"

# Check if the DMG was created
if [ -f "release/"*"/Storme Engine-"*".dmg" ]; then
    echo "💿 DMG file created successfully"
    echo "📂 Opening release folder..."
    open release/
else
    echo "⚠️  DMG file not found, but build may have succeeded"
fi
