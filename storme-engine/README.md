# 🎮 Storme Engine - Desktop Game Launcher

A modern, native desktop game launcher and platform built specifically for **macOS (Apple Silicon M1/M2)** and other platforms. Built with Electron, React, and TypeScript.

## ✨ Features

### 🎯 Core Features
- **Native macOS Experience**: Optimized for Apple Silicon (M1/M2) Macs
- **Game Library Management**: Browse, install, and launch games seamlessly
- **User Authentication**: Secure login and user profiles
- **Dual Dashboard**: Separate interfaces for Gamers and Developers
- **Loading Screens**: Beautiful, branded loading experiences
- **Real-time Updates**: Live notifications and game updates

### 🎮 For Gamers
- **Games Library**: Browse and discover new games
- **User Dashboard**: Track playtime, achievements, and progress
- **Quick Launch**: Fast access to your favorite games
- **Social Features**: Connect with friends and community

### 👨‍💻 For Developers
- **Developer Dashboard**: Analytics, monetization, and project management
- **AI Marketing Tools**: Intelligent marketing assistance
- **Revenue Tracking**: Monitor downloads and earnings
- **Project Management**: Organize and publish your games

## 🚀 Quick Start

### Prerequisites
- **macOS 10.15+** (optimized for Apple Silicon M1/M2)
- **Node.js 18+**
- **npm or yarn**

### Installation & Build

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd storme-engine
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Development mode**
   ```bash
   npm run dev
   ```

4. **Build for macOS** (Recommended)
   ```bash
   ./build.sh
   ```
   Or manually:
   ```bash
   npm run dist:mac
   ```

### 📱 Platform-Specific Builds

- **macOS (Universal - Intel + Apple Silicon)**: `npm run dist:mac`
- **Windows**: `npm run dist:win`
- **Linux**: `npm run dist:linux`
- **All platforms**: `npm run dist`

## 🏗️ Architecture

```
Storme Engine
├── Main Process (Electron)     # System integration & IPC
├── Renderer Process (React)    # User interface
├── Preload Scripts            # Secure IPC bridge
└── Common Types & Utilities   # Shared code
```

### 📁 Project Structure
```
storme-engine/
├── src/
│   ├── main/           # Electron main process
│   ├── renderer/       # React application
│   ├── preload/        # Preload scripts
│   └── common/         # Shared utilities
├── build/              # Build assets & configuration
├── release/            # Built applications
└── scripts/            # Build and utility scripts
```

## 🛠️ Tech Stack

- **Framework**: Electron 25+
- **Frontend**: React 18 + TypeScript
- **UI Library**: Material-UI (MUI)
- **Build Tool**: Vite
- **Bundler**: Electron Builder
- **Styling**: Styled Components
- **State Management**: Zustand
- **Routing**: React Router

## 🎨 Key Pages & Features

### 🔐 Authentication
- **Login Page**: Secure user authentication
- **Register Page**: New user registration
- **Forgot Password**: Password recovery

### 🎮 Gaming Features
- **Games Page**: Browse and discover games with beautiful loading screen
- **Game Details**: Detailed game information and installation
- **Game Library**: Personal game collection management

### 📊 Dashboards
- **Gamer Dashboard**:
  - Game statistics and achievements
  - Recent games and playtime tracking
  - Level progression and experience
  - Quick actions and shortcuts

- **Developer Dashboard**:
  - Project management and analytics
  - Revenue and download tracking
  - AI marketing tools
  - User engagement metrics

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production

# Distribution
npm run dist            # Build for all platforms
npm run dist:mac        # Build for macOS (Universal)
npm run dist:win        # Build for Windows
npm run dist:linux      # Build for Linux

# Utilities
npm start               # Start built application
npm run postinstall     # Install app dependencies
```

### 🐛 Debugging

- **Main Process**: Use VS Code debugger or `console.log`
- **Renderer Process**: Use Chrome DevTools (`Cmd+Option+I`)
- **IPC Communication**: Check `src/common/ipc-channels.ts`

## 📦 Building for Distribution

### macOS (Recommended for M1 Macs)

1. **Quick Build**:
   ```bash
   ./build.sh
   ```

2. **Manual Build**:
   ```bash
   npm install
   npm run build
   npm run dist:mac
   ```

3. **Output**: `release/[version]/Storme Engine-[version].dmg`

### Code Signing (macOS)
For distribution outside the App Store, you'll need:
- Apple Developer ID certificate
- Proper entitlements (already configured in `build/entitlements.mac.plist`)

## 🎯 Roadmap

- [ ] **Game Installation System**: Direct game downloads and installation
- [ ] **Cloud Save Sync**: Cross-device game save synchronization
- [ ] **Social Features**: Friends, chat, and community integration
- [ ] **Mod Support**: Game modification and custom content
- [ ] **VR Integration**: Virtual reality game support
- [ ] **Streaming**: Game streaming capabilities

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on macOS
5. Submit a pull request

## 📄 License

Copyright © 2025 GameStorme. All rights reserved.

## 🆘 Support

- **Issues**: Report bugs and feature requests
- **Documentation**: Check the `/docs` folder
- **Community**: Join our Discord server

---

**Built with ❤️ for the gaming community by GameStorme**
