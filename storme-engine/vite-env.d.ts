/// <reference types="vite/client" />

// Import and extend the global window interface
declare global {
  interface Window {
    // Add any global window properties here
    __APP_VERSION__: string;
    __APP_NAME__: string;
    __APP_ENV__: 'development' | 'production' | 'test';
    
    // Electron API bridge
    electron: {
      // System info
      getAppVersion: () => Promise<string>;
      getPlatform: () => Promise<string>;
      
      // Window controls
      minimize: () => void;
      maximize: () => void;
      close: () => void;
      
      // File system
      selectDirectory: () => Promise<string | null>;
      readFile: (path: string) => Promise<string>;
      writeFile: (path: string, content: string) => Promise<void>;
      
      // IPC communication
      on: (channel: string, callback: (...args: any[]) => void) => void;
      removeListener: (channel: string, callback: (...args: any[]) => void) => void;
      
      // Game management
      launchGame: (gameId: string, options?: Record<string, any>) => Promise<void>;
      installGame: (gameId: string, options?: Record<string, any>) => Promise<void>;
      uninstallGame: (gameId: string, options?: Record<string, any>) => Promise<void>;
      getGameStatus: (gameId: string) => Promise<{
        installed: boolean;
        version?: string;
        path?: string;
        size?: number;
        lastPlayed?: number;
      }>;
      
      // Settings
      getSettings: () => Promise<Record<string, any>>;
      updateSettings: (settings: Record<string, any>) => Promise<void>;
      
      // Updates
      checkForUpdates: () => Promise<{
        updateAvailable: boolean;
        version?: string;
        releaseNotes?: string;
      }>;
      downloadUpdate: () => Promise<void>;
      installUpdate: () => Promise<void>;
      
      // Authentication
      login: (credentials: { email: string; password: string }) => Promise<{
        success: boolean;
        user?: {
          id: string;
          email: string;
          name: string;
          avatar?: string;
        };
        error?: string;
      }>;
      
      logout: () => Promise<void>;
      getAuthStatus: () => Promise<{
        isAuthenticated: boolean;
        user?: {
          id: string;
          email: string;
          name: string;
          avatar?: string;
        };
      }>;
    };
  }
  
  // Global type for environment variables
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      VITE_APP_NAME: string;
      VITE_APP_VERSION: string;
      VITE_APP_HOMEPAGE: string;
      VITE_API_BASE_URL: string;
      VITE_WS_BASE_URL: string;
      VITE_APP_DEBUG: string;
    }
  }
}

// Declare types for imported assets
declare module '*.svg' {
  import React = require('react');
  export const ReactComponent: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  const src: string;
  export default src;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.jpeg' {
  const content: string;
  export default content;
}

declare module '*.gif' {
  const content: string;
  export default content;
}

declare module '*.webp' {
  const content: string;
  export default content;
}

declare module '*.ico' {
  const content: string;
  export default content;
}

declare module '*.bmp' {
  const content: string;
  export default content;
}

// Declare types for CSS modules
declare module '*.module.css' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module '*.module.scss' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module '*.module.sass' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

// Declare types for other assets
declare module '*.woff';
declare module '*.woff2';
declare module '*.eot';
declare module '*.ttf';
declare module '*.otf';

// Make sure TypeScript treats this file as a module
export {};
