{"root": true, "env": {"browser": true, "es2021": true, "node": true, "electron": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}, "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "react", "react-hooks", "import", "prettier"], "settings": {"react": {"version": "detect"}, "import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"], "moduleDirectory": ["node_modules", "src/"]}, "typescript": {"alwaysTryTypes": true}}}, "rules": {"prettier/prettier": "warn", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}, {"pattern": "@/**", "group": "internal"}, {"pattern": "@main/**", "group": "internal"}, {"pattern": "@renderer/**", "group": "internal"}, {"pattern": "@common/**", "group": "internal"}], "pathGroupsExcludedImportTypes": ["react"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}]}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "rules": {"@typescript-eslint/explicit-function-return-type": ["warn"], "@typescript-eslint/explicit-module-boundary-types": ["warn"]}}, {"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-explicit-any": "off"}}]}