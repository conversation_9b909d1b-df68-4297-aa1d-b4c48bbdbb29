# Production Environment Variables
# This file overrides .env in production mode

# Application
NODE_ENV=production
VITE_APP_DEBUG=false

# API Configuration - Update these with your production API endpoints
VITE_API_BASE_URL=https://api.gamestorme.com/v1
VITE_WS_BASE_URL=wss://api.gamestorme.com

# Electron
ELECTRON_IS_DEV=false

# Build Configuration
VITE_SOURCE_MAP=false
VITE_DROP_CONSOLE=true
VITE_DROP_DEBUGGER=true

# Feature Flags
VITE_FEATURE_UPDATES=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_OFFLINE_MODE=true

# Performance
VITE_COMPRESSION=true
VITE_CHUNK_SIZE_WARNING_LIMIT=1000

# Analytics - Update these with your production analytics IDs
VITE_GOOGLE_ANALYTICS_ID=UA-XXXXXXXXX-Y
VITE_SENTRY_DSN=your-production-sentry-dsn

# Note: Sensitive values should be provided at build time or through a secure secret management system.
