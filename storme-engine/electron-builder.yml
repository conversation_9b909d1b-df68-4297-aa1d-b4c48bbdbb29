appId: com.gamestorme.storme
productName: Storme Engine
copyright: Copyright © 2025 Gamestorme

directories:
  output: release/${version}
  buildResources: build

files:
  - "dist/**/*"
  - "src/main/**/*"
  - "src/common/**/*"
  - "src/renderer/.next/**/*"
  - "src/renderer/public/**/*"
  - "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}"
  - "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}"
  - "!**/node_modules/.cache"
  - "!**/node_modules/.bin"

extraResources:
  - from: "src/renderer/.next"
    to: "app/.next"
    filter:
      - "**/*"

asar: true
asarUnpack:
  - "**/node_modules/electron-updater/**"
  - "**/node_modules/electron/**"

fileAssociations:
  - ext: gsgame
    name: Storme Game
    description: Storme Game Package
    role: Editor

win:
  target: 
    - target: nsis
      arch:
        - x64
  icon: "build/icon.ico"
  publisherName: Gamestorme
  fileAssociations:
    - ext: gsgame
      name: Storme Game
      description: Storme Game Package
      role: Editor

mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: "build/icon.icns"
  category: public.app-category.games
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: "build/entitlements.mac.plist"
  entitlementsInherit: "build/entitlements.mac.plist"
  extendInfo:
    NSCameraUsageDescription: "The app needs access to the camera for some features"
    NSMicrophoneUsageDescription: "The app needs access to the microphone for voice chat"

linux:
  target:
    - AppImage
    - deb
  icon: "build/icons/"
  category: Game
  maintainer: <EMAIL>
  vendor: Gamestorme
  synopsis: Storme Game Engine
  description: A modern game launcher and platform
  desktop:
    Name: Storme Engine
    Comment: "Launch and manage your games"
    Categories: Game;
    Keywords: game;launcher;storme;gamestorme;
    MimeType: application/x-storme-game;

publish:
  provider: github
  repo: storme-engine
  owner: gamestorme
  private: false
  releaseType: draft

nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Storme Engine
  uninstallDisplayName: Storme Engine
  include: "build/installer.nsh"
  license: "LICENSE"
  artifactName: "${productName}-Setup-${version}.${ext}"
