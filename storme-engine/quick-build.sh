#!/bin/bash

# Quick build script for Storme Engine (development mode)
echo "🚀 Quick build for Storme Engine..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm install --silent

echo "🔨 Building renderer only (skipping TypeScript checks for speed)..."
cd src/renderer
npm run build --silent
cd ../..

echo "🎮 Starting Storme Engine in development mode..."
npm run dev

echo "✅ Storme Engine is running!"
