import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

export default defineConfig({
  root: 'src/renderer',
  base: './', // Use relative paths for loading assets
  publicDir: 'public',
  plugins: [
    react(),
    // Polyfill Node.js built-in modules for the Renderer process
    nodePolyfills({
      protocolImports: true,
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/renderer/src'),
      '@components': resolve(__dirname, 'src/renderer/src/components'),
      '@pages': resolve(__dirname, 'src/renderer/src/pages'),
      '@hooks': resolve(__dirname, 'src/renderer/src/hooks'),
      '@utils': resolve(__dirname, 'src/renderer/src/utils'),
      '@styles': resolve(__dirname, 'src/renderer/src/styles'),
      '@assets': resolve(__dirname, 'src/renderer/public'),
      '@main': resolve(__dirname, 'src/main'),
      '@common': resolve(__dirname, 'src/common'),
    },
  },
  build: {
    outDir: '../../dist/renderer',
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/renderer/index.html'),
      },
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-slot'],
          utils: ['date-fns', 'lodash', 'zod'],
          state: ['@tanstack/react-query', 'jotai', 'zod'],
        },
      },
    },
  },
  server: {
    port: 3000,
    strictPort: true,
    open: false,
    proxy: {
      // Example API proxy
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        secure: false,
        ws: true,
      },
    },
  },
  define: {
    'process.env': {},
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __APP_NAME__: JSON.stringify(process.env.npm_package_name),
    __APP_ENV__: JSON.stringify(process.env.NODE_ENV || 'development'),
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @import "@/styles/variables.scss";
          @import "@/styles/mixins.scss";
        `,
      },
    },
    modules: {
      localsConvention: 'camelCaseOnly',
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './test/setup.ts',
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        '**/*.d.ts',
        '**/*.test.{js,ts,jsx,tsx}',
        '**/test/**',
        '**/__tests__/**',
        '**/*.spec.{js,ts,jsx,tsx}',
      ],
    },
  },
});
