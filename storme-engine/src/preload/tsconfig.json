{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "../../dist/preload", "rootDir": ".", "sourceMap": true, "module": "commonjs", "target": "es2020", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@preload/*": ["./*"], "@common/*": ["../common/*"]}, "types": ["node", "electron"]}, "include": ["**/*.ts"], "exclude": ["node_modules"]}