import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API that will be exposed to the renderer process
const api = {
  // Window controls
  minimize: () => ipcRenderer.send('window-minimize'),
  maximize: () => ipcRenderer.send('window-maximize'),
  close: () => ipcRenderer.send('window-close'),
  isMaximized: () => ipcRenderer.invoke('window-is-maximized'),
  
  // Game management
  getGames: () => ipcRenderer.invoke('get-games'),
  launchGame: (gameId: string) => ipcRenderer.invoke('launch-game', gameId),
  
  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  updateSettings: (settings: any) => ipcRenderer.invoke('update-settings', settings),
  
  // Authentication
  login: (credentials: { email: string; password: string }) => 
    ipcRenderer.invoke('login', credentials),
  
  // Auto-update
  onUpdateAvailable: (callback: () => void) => 
    ipcRenderer.on('update-available', callback),
  onUpdateDownloaded: (callback: () => void) => 
    ipcRenderer.on('update-downloaded', callback),
  restartAndUpdate: () => ipcRenderer.send('restart-and-update'),
  
  // System information
  getSystemInfo: () => ({
    platform: process.platform,
    arch: process.arch,
    version: process.versions.electron,
    appVersion: '1.0.0', // TODO: Get from package.json
  }),
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', api);

// Type declarations for TypeScript
declare global {
  interface Window {
    electronAPI: typeof api;
  }
}
