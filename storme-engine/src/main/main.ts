import { app, BrowserWindow, ipc<PERSON>ain, shell, Tray, Menu, nativeImage } from 'electron';
import path from 'path';
import isDev from 'electron-is-dev';
import { setupTitlebar, attachTitlebarToWindow } from 'custom-electron-titlebar/main';

// Set up the custom title bar
setupTitlebar();

// Paths
declare const MAIN_WINDOW_WEBPACK_ENTRY: string;
declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;

// Constants
const WINDOW_WIDTH = 1280;
const WINDOW_HEIGHT = 800;
const MIN_WINDOW_WIDTH = 1024;
const MIN_WINDOW_HEIGHT = 768;

// Handle creating/removing shortcuts on Windows when installing/uninstalling
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Set application name
app.name = 'Storme Engine';

// Set application version
if (process.env.APP_VERSION) {
  app.setVersion(process.env.APP_VERSION);
}

// Set application path
app.setPath('userData', path.join(app.getPath('appData'), 'Storme Engine'));

// Single instance lock
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
  process.exit(0);
}

// Global window reference
let mainWindow: BrowserWindow | null = null;
let tray: Tray | null = null;
let isQuitting = false;

// Create the main application window
const createWindow = async () => {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: WINDOW_WIDTH,
    height: WINDOW_HEIGHT,
    minWidth: MIN_WINDOW_WIDTH,
    minHeight: MIN_WINDOW_HEIGHT,
    show: false, // Don't show until ready-to-show
    frame: false, // We'll use our own title bar
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#0A0A1A',
      symbolColor: '#FFFFFF',
      height: 32
    },
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: !isDev,
      devTools: isDev,
    },
    backgroundColor: '#0A0A1A',
    icon: path.join(__dirname, '../renderer/assets/icon.png')
  });

  // Load the index.html
  mainWindow.loadURL(MAIN_WINDOW_WEBPACK_ENTRY);

  // Setup window events
  setupWindowEvents();

  // Setup IPC handlers
  setupIpcHandlers();

  // Setup auto-updates in production
  if (!isDev) {
    require('./services/update-service').setupAutoUpdater(mainWindow);
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
    }
  });

  // Setup tray icon
  setupTray();
};

// Setup window event handlers
const setupWindowEvents = () => {
  if (!mainWindow) return;

  mainWindow.on('close', (event) => {
    if (!isQuitting) {
      event.preventDefault();
      mainWindow?.hide();
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
};

// Setup system tray
const setupTray = () => {
  if (tray) return;

  const iconPath = path.join(__dirname, '../renderer/assets/icons/tray.png');
  const trayIcon = nativeImage.createFromPath(iconPath).resize({ width: 16, height: 16 });
  
  tray = new Tray(trayIcon);
  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Open Storme Engine',
      click: () => {
        if (mainWindow) {
          if (mainWindow.isMinimized()) mainWindow.restore();
          mainWindow.show();
          mainWindow.focus();
        } else {
          createWindow();
        }
      }
    },
    { type: 'separator' },
    {
      label: 'Quit',
      click: () => {
        isQuitting = true;
        app.quit();
      }
    }
  ]);

  tray.setToolTip('Storme Engine');
  tray.setContextMenu(contextMenu);
  
  tray.on('click', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.show();
      mainWindow.focus();
    }
  });
};

// Setup IPC handlers
const setupIpcHandlers = () => {
  // Window control handlers
  ipcMain.handle('window:minimize', () => {
    mainWindow?.minimize();
  });

  ipcMain.handle('window:maximize', () => {
    if (mainWindow?.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow?.maximize();
    }
  });

  ipcMain.handle('window:close', () => {
    mainWindow?.close();
  });

  // Open external links in default browser
  ipcMain.handle('open-external', (_, url) => {
    shell.openExternal(url);
  });
};

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Rejection:', reason);
});

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  // Create the main window
  await createWindow();

  // On macOS it's common to re-create a window when the dock icon is clicked
  await setupSession();
  
  // Setup Sentry for error tracking
  if (!isDev) {
    setupSentry();
  }
  
  // Setup analytics
  setupAnalytics();
  
  // Setup crash reporter
  setupCrashReporter();
  
  // Create the browser window
  mainWindow = createWindow();
  
  // Setup auto launch
  setupAutoLaunch();
  
  // Setup auto updater
  if (!isDev) {
    setupAutoUpdater();
  }
  
  // Setup global shortcuts
  setupGlobalShortcuts();
  
  // Setup application menu
  setupMenu();
  
  // Setup system tray
  tray = setupTray(mainWindow);
  
  // Setup power monitor
  setupPowerMonitor();
  
  // Setup IPC handlers
  setupIpcHandlers();
  
  // Emitted when the window is closed.
  mainWindow.on('closed', () => {
    // Dereference the window object, usually you would store windows
    // in an array if your app supports multi windows, this is the time
    // when you should delete the corresponding element.
    mainWindow = null;
  });
  
  // Create window on macOS when the dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      mainWindow = createWindow();
    }
  });
  
  // Open external links in the default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:') || url.startsWith('http:')) {
      shell.openExternal(url);
    }
    return { action: 'deny' };
  });
  
  // Show window when page is ready
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
      
      // Auto-open DevTools in development mode
      if (isDev) {
        mainWindow.webContents.openDevTools({ mode: 'detach' });
      }
    }
  });
  
  // Handle window being closed by the user
  mainWindow.on('close', (event) => {
    if (!isQuitting) {
      event.preventDefault();
      
      if (mainWindow) {
        mainWindow.hide();
      }
    }
    
    return false;
  });
  
  // Handle window being minimized
  mainWindow.on('minimize', (event: Event) => {
    if (process.platform === 'darwin') {
      event.preventDefault();
      if (mainWindow) {
        mainWindow.hide();
      }
    }
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Exit cleanly on request from parent process in development mode.
if (isDev) {
  if (process.platform === 'win32') {
    process.on('message', (data) => {
      if (data === 'graceful-exit') {
        app.quit();
      }
    });
  } else {
    process.on('SIGTERM', () => {
      app.quit();
    });
  }
}

// Handle the app before it quits
app.on('before-quit', () => {
  isQuitting = true;
  
  // Clean up resources
  if (tray) {
    tray.destroy();
    tray = null;
  }
  
  // Save window state
  if (mainWindow) {
    // Save window state here if needed
  }
});

// Handle deep linking
app.on('open-url', (event, url) => {
  event.preventDefault();
  
  // Parse the URL and handle the deep link
  console.log('Deep link opened:', url);
  
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.focus();
    
    // Send the URL to the renderer process
    mainWindow.webContents.send('deep-link', url);
  }
});

// Handle second instance
app.on('second-instance', (event, commandLine, workingDirectory) => {
  // Someone tried to run a second instance, we should focus our window.
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.focus();
  }
  
  // Handle command line arguments
  const deepLink = commandLine.pop();
  if (deepLink && deepLink.startsWith('storme://')) {
    // Handle deep link
    console.log('Deep link from second instance:', deepLink);
    
    if (mainWindow) {
      mainWindow.webContents.send('deep-link', deepLink);
    }
  }
});

// Handle when the app is activated (macOS)
app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    mainWindow = createWindow();
  }
});

// Handle when all windows are closed
app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle when the app is about to quit
app.on('will-quit', (event) => {
  // Perform any cleanup here
});

// Handle when the app is ready to show
app.on('ready', () => {
  // App is ready
});

// Handle when the app is finished launching
app.on('did-finish-launching', () => {
  // App has finished launching
});

// Handle when the app is about to quit
app.on('before-quit', () => {
  // App is about to quit
});

// Handle when the app is quitting
app.on('will-quit', () => {
  // App is quitting
});

// Handle when all windows are closed
app.on('window-all-closed', () => {
  // All windows have been closed
});

// Handle when the app is activated
app.on('activate', () => {
  // App has been activated
});

// Export the main window for use in other modules
export { mainWindow };

// Export the app version
export const appVersion = app.getVersion();

// Export the app name
export const appName = app.getName();

// Export the app path
export const appPath = app.getAppPath();

// Export the user data path
export const userDataPath = app.getPath('userData');

// Export the documents path
export const documentsPath = app.getPath('documents');

// Export the downloads path
export const downloadsPath = app.getPath('downloads');

// Export the desktop path
export const desktopPath = app.getPath('desktop');

// Export the home path
export const homePath = app.getPath('home');

// Export the temp path
export const tempPath = app.getPath('temp');

// Export the exe path
export const exePath = app.getPath('exe');

// Export the module path
export const modulePath = app.getPath('module');
