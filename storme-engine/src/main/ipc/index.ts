import { ipcMain, dialog, shell, app, BrowserWindow } from 'electron';
import { autoUpdater } from 'electron-updater';
import { IPC_CHANNELS } from '../../../common/ipc-channels';
import { getMainWindow } from '../window';
import path from 'path';
import fs from 'fs-extra';

export function setupIpcHandlers() {
  // App handlers
  ipcMain.handle(IPC_CHANNELS.APP_GET_VERSION, () => app.getVersion());
  ipcMain.handle(IPC_CHANNELS.APP_GET_NAME, () => app.getName());
  ipcMain.handle(IPC_CHANNELS.APP_GET_PATH, (_, name: string) => app.getPath(name as any));
  ipcMain.handle(IPC_CHANNELS.APP_RELAUNCH, () => {
    app.relaunch();
    app.quit();
  });
  ipcMain.handle(IPC_CHANNELS.APP_QUIT, () => app.quit());

  // Window handlers
  ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, () => {
    const win = getMainWindow();
    if (win) win.minimize();
  });
  
  ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, () => {
    const win = getMainWindow();
    if (win) {
      if (win.isMaximized()) {
        win.unmaximize();
      } else {
        win.maximize();
      }
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, () => {
    const win = getMainWindow();
    if (win) win.close();
  });
  
  ipcMain.handle(IPC_CHANNELS.WINDOW_TOGGLE_FULLSCREEN, () => {
    const win = getMainWindow();
    if (win) {
      const isFullscreen = win.isFullScreen();
      win.setFullScreen(!isFullscreen);
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS, () => {
    const win = getMainWindow();
    if (win) {
      if (win.webContents.isDevToolsOpened()) {
        win.webContents.closeDevTools();
      } else {
        win.webContents.openDevTools({ mode: 'detach' });
      }
    }
  });

  // File system handlers
  ipcMain.handle(IPC_CHANNELS.FS_READ_FILE, async (_, filePath: string, options?: any) => {
    try {
      return await fs.readFile(filePath, options);
    } catch (error) {
      console.error('Error reading file:', error);
      throw error;
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.FS_WRITE_FILE, async (_, filePath: string, data: any, options?: any) => {
    try {
      await fs.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, data, options);
      return true;
    } catch (error) {
      console.error('Error writing file:', error);
      throw error;
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.FS_READ_DIR, async (_, dirPath: string) => {
    try {
      return await fs.readdir(dirPath);
    } catch (error) {
      console.error('Error reading directory:', error);
      throw error;
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.FS_MKDIR, async (_, dirPath: string, options?: any) => {
    try {
      await fs.mkdirp(dirPath, options);
      return true;
    } catch (error) {
      console.error('Error creating directory:', error);
      throw error;
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.FS_STAT, async (_, path: string) => {
    try {
      return await fs.stat(path);
    } catch (error) {
      console.error('Error getting file stats:', error);
      throw error;
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.FS_UNLINK, async (_, path: string) => {
    try {
      await fs.unlink(path);
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.FS_RENAME, async (_, oldPath: string, newPath: string) => {
    try {
      await fs.rename(oldPath, newPath);
      return true;
    } catch (error) {
      console.error('Error renaming file:', error);
      throw error;
    }
  });

  // Dialog handlers
  ipcMain.handle(IPC_CHANNELS.DIALOG_SHOW_OPEN_DIALOG, (_, options: Electron.OpenDialogOptions) => {
    const win = getMainWindow();
    return dialog.showOpenDialog(win || undefined, options);
  });
  
  ipcMain.handle(IPC_CHANNELS.DIALOG_SHOW_SAVE_DIALOG, (_, options: Electron.SaveDialogOptions) => {
    const win = getMainWindow();
    return dialog.showSaveDialog(win || undefined, options);
  });
  
  ipcMain.handle(IPC_CHANNELS.DIALOG_SHOW_MESSAGE_BOX, (_, options: Electron.MessageBoxOptions) => {
    const win = getMainWindow();
    return dialog.showMessageBox(win || undefined, options);
  });
  
  ipcMain.handle(IPC_CHANNELS.DIALOG_SHOW_ERROR_BOX, (_, title: string, content: string) => {
    dialog.showErrorBox(title, content);
  });

  // Shell handlers
  ipcMain.handle(IPC_CHANNELS.SHELL_OPEN_EXTERNAL, (_, url: string, options?: Electron.OpenExternalOptions) => {
    return shell.openExternal(url, options);
  });
  
  ipcMain.handle(IPC_CHANNELS.SHELL_OPEN_PATH, (_, path: string) => {
    return shell.openPath(path);
  });
  
  ipcMain.handle(IPC_CHANNELS.SHELL_SHOW_ITEM_IN_FOLDER, (_, fullPath: string) => {
    return shell.showItemInFolder(fullPath);
  });
  
  ipcMain.handle(IPC_CHANNELS.SHELL_BE_ITEM_IN_FOLDER, (_, fullPath: string) => {
    return shell.beepItemInFolder(fullPath);
  });

  // Auto-update handlers
  ipcMain.handle(IPC_CHANNELS.UPDATE_CHECK, async () => {
    if (process.env.NODE_ENV === 'development') {
      return { updateInfo: { version: app.getVersion() }, updateAvailable: false };
    }
    
    try {
      const updateCheckResult = await autoUpdater.checkForUpdates();
      return { 
        updateInfo: updateCheckResult.updateInfo, 
        updateAvailable: true 
      };
    } catch (error) {
      console.error('Error checking for updates:', error);
      return { 
        error: error.message, 
        updateAvailable: false 
      };
    }
  });
  
  ipcMain.handle(IPC_CHANNELS.UPDATE_DOWNLOAD, async () => {
    return new Promise((resolve, reject) => {
      autoUpdater.once('update-downloaded', () => {
        resolve({ success: true });
      });
      
      autoUpdater.once('error', (error) => {
        reject(new Error(`Failed to download update: ${error.message}`));
      });
      
      autoUpdater.downloadUpdate().catch(reject);
    });
  });
  
  ipcMain.handle(IPC_CHANNELS.UPDATE_INSTALL, () => {
    autoUpdater.quitAndInstall();
    return { success: true };
  });

  // Game management handlers
  ipcMain.handle(IPC_CHANNELS.GAME_GET_ALL, async () => {
    // Implement game fetching logic here
    return [];
  });
  
  ipcMain.handle(IPC_CHANNELS.GAME_GET_BY_ID, async (_, id: string) => {
    // Implement game fetching by ID logic here
    return null;
  });
  
  ipcMain.handle(IPC_CHANNELS.GAME_INSTALL, async (_, gameId: string) => {
    // Implement game installation logic here
    return { success: true, gameId };
  });
  
  ipcMain.handle(IPC_CHANNELS.GAME_UNINSTALL, async (_, gameId: string) => {
    // Implement game uninstallation logic here
    return { success: true, gameId };
  });
  
  ipcMain.handle(IPC_CHANNELS.GAME_LAUNCH, async (_, gameId: string) => {
    // Implement game launch logic here
    return { success: true, gameId };
  });
  
  // Settings handlers
  ipcMain.handle(IPC_CHANNELS.SETTINGS_GET, async (_, key: string) => {
    // Implement settings getter logic here
    return null;
  });
  
  ipcMain.handle(IPC_CHANNELS.SETTINGS_SET, async (_, key: string, value: any) => {
    // Implement settings setter logic here
    return { success: true, key, value };
  });
  
  ipcMain.handle(IPC_CHANNELS.SETTINGS_RESET, async () => {
    // Implement settings reset logic here
    return { success: true };
  });

  // System information handlers
  ipcMain.handle(IPC_CHANNELS.SYSTEM_GET_INFO, async () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: process.getSystemVersion(),
      totalMemory: process.getSystemMemoryInfo().total,
      freeMemory: process.getSystemMemoryInfo().free,
      cpuUsage: process.getCPUUsage(),
      env: {
        node: process.versions.node,
        chrome: process.versions.chrome,
        electron: process.versions.electron,
      },
    };
  });
  
  ipcMain.handle(IPC_CHANNELS.SYSTEM_GET_USAGE, async () => {
    const win = getMainWindow();
    return {
      memory: process.getProcessMemoryInfo(),
      cpu: process.getCPUUsage(),
      heap: process.getHeapStatistics(),
      windowBounds: win?.getBounds(),
    };
  });

  // Analytics handlers
  ipcMain.handle(IPC_CHANNELS.ANALYTICS_TRACK, (_, event: string, properties?: any) => {
    // Implement analytics tracking logic here
    console.log(`[Analytics] ${event}`, properties);
    return { success: true };
  });
  
  ipcMain.handle(IPC_CHANNELS.ANALYTICS_PAGE_VIEW, (_, page: string, properties?: any) => {
    // Implement page view tracking logic here
    console.log(`[Analytics] Page View: ${page}`, properties);
    return { success: true };
  });

  // Error reporting handlers
  ipcMain.handle(IPC_CHANNELS.ERROR_REPORT, (_, error: Error, context?: any) => {
    // Implement error reporting logic here
    console.error('Error reported:', error, context);
    return { success: true };
  });

  // Logging handler
  ipcMain.handle(IPC_CHANNELS.LOG, (_, level: string, message: string, ...args: any[]) => {
    const logMessage = `[Renderer] ${message}`;
    switch (level) {
      case 'debug':
        console.debug(logMessage, ...args);
        break;
      case 'info':
        console.info(logMessage, ...args);
        break;
      case 'warn':
        console.warn(logMessage, ...args);
        break;
      case 'error':
        console.error(logMessage, ...args);
        break;
      default:
        console.log(logMessage, ...args);
    }
    return { success: true };
  });

  // Generic message handler
  ipcMain.handle(IPC_CHANNELS.MESSAGE, (_, channel: string, ...args: any[]) => {
    // Handle custom messages here
    console.log(`Received message on channel ${channel}:`, ...args);
    return { success: true };
  });
}

export function sendToRenderer(channel: string, ...args: any[]) {
  const win = getMainWindow();
  if (win && !win.isDestroyed()) {
    win.webContents.send(channel, ...args);
  }
}
