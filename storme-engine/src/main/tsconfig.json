{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "../../dist/main", "rootDir": ".", "sourceMap": true, "module": "commonjs", "target": "es2020", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@main/*": ["./*"], "@common/*": ["../common/*"], "@renderer/*": ["../renderer/src/*"]}, "types": ["node", "electron", "electron-builder"]}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "**/*.test.ts", "**/*.spec.ts"]}