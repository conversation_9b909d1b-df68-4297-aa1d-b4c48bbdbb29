import { app, BrowserWindow, ipcMain, dialog, screen, Tray, Menu, shell, nativeImage, MessageBoxOptions, OpenDialogOptions, MessageBoxReturnValue } from 'electron';
import path from 'path';
import { autoUpdater } from 'electron-updater';
import log from 'electron-log';
import Store from 'electron-store';

type ThemeMode = 'light' | 'dark';

interface AppSettings {
  autoUpdate: boolean;
  launchOnStartup: boolean;
  minimizeToTray: boolean;
  theme: ThemeMode;
}

// Define the AppConfig interface
export interface AppConfig {
  windowBounds: {
    width: number;
    height: number;
    x?: number;
    y?: number;
  };
  settings: AppSettings;
  games: {
    installPath: string;
    recentGames: string[];
  };
}

// Initialize logger
log.initialize();
log.transports.file.level = 'info';
log.info('App starting...');

// Extend the Store type to include our methods
interface TypedStore<T extends Record<string, any>> extends Store<T> {
  get<K extends keyof T>(key: K): T[K];
  set<K extends keyof T>(key: K, value: T[K]): void;
  store: T;
}

// Initialize config store
const store: TypedStore<AppConfig> = new Store<AppConfig>({
  defaults: {
    windowBounds: {
      width: 1280,
      height: 800,
      x: undefined,
      y: undefined
    },
    settings: {
      autoUpdate: true,
      minimizeToTray: true,
      launchOnStartup: false,
      theme: 'system'
    },
    games: {
      installPath: '',
      recentGames: []
    }
  }
}) as TypedStore<AppConfig>;

// Add store methods for type safety
const typedStore = {
  get: <K extends keyof AppConfig>(key: K): AppConfig[K] => store.get(key),
  set: <K extends keyof AppConfig>(key: K, value: AppConfig[K]): void => {
    store.set(key, value);
  },
  store: store.store
};

// Global references
let mainWindow: BrowserWindow | null = null;
let tray: Tray | null = null;
let isQuitting = false;

// Game Engine Configuration
const GAME_ENGINE_CONFIG = {
  name: 'Storme Engine',
  version: '1.0.0',
  configDirectory: path.join(app.getPath('userData'), 'config'),
  logsDirectory: path.join(app.getPath('userData'), 'logs'),
};

// Initialize directories
function initializeDirectories() {
  const directories = [
    GAME_ENGINE_CONFIG.configDirectory,
    GAME_ENGINE_CONFIG.logsDirectory,
    typedStore.get('games').installPath,
  ];

  directories.forEach((dir) => {
    try {
      if (!require('fs').existsSync(dir)) {
        require('fs').mkdirSync(dir, { recursive: true });
        log.info(`Created directory: ${dir}`);
      }
    } catch (error) {
      log.error(`Error creating directory ${dir}:`, error);
    }
  });
}

// Create the main browser window
async function createWindow() {
  const { width, height, x, y } = typedStore.get('windowBounds');
  const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;
  
  // Calculate window position (center if not set or invalid)
  const windowX = x && x >= 0 && x <= screenWidth ? x : Math.max(0, Math.floor((screenWidth - width) / 2));
  const windowY = y && y >= 0 && y <= screenHeight ? y : Math.max(0, Math.floor((screenHeight - height) / 2));

  // Create the browser window
  mainWindow = new BrowserWindow({
    width,
    height,
    x: windowX,
    y: windowY,
    minWidth: 1024,
    minHeight: 768,
    show: false, // Don't show until ready-to-show
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#0A0A1A',
      symbolColor: '#FFFFFF',
      height: 32
    },
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: !process.env.ELECTRON_IS_DEV,
      devTools: !!process.env.ELECTRON_IS_DEV,
    },
    backgroundColor: '#0A0A1A',
    icon: path.join(__dirname, '../renderer/assets/icon.png')
  });

  // Load the app
  if (process.env.ELECTRON_IS_DEV) {
    await mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools({ mode: 'detach' });
  } else {
    await mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
      
      // Check for updates after the window is shown
      if (typedStore.get('settings').autoUpdate && !process.env.ELECTRON_IS_DEV) {
        checkForUpdates();
      }
    }
  });

  // Save window bounds when window is resized or moved
  mainWindow.on('resize', saveWindowBounds);
  mainWindow.on('move', saveWindowBounds);

  // Handle window close
  mainWindow.on('close', (event) => {
    if (!isQuitting && typedStore.get('settings').minimizeToTray) {
      event.preventDefault();
      mainWindow?.hide();
    }
  });

  // Clean up on window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Setup window controls and tray
  setupWindowControls();
  setupTray();
  
  // Setup IPC handlers
  setupIpcHandlers();
  
  // Setup auto-updater
  setupAutoUpdater();
  
  return mainWindow;
}

// Save window bounds
function saveWindowBounds() {
  if (mainWindow) {
    typedStore.set('windowBounds', mainWindow.getBounds());
  }
}

// Check for updates
function checkForUpdates() {
  if (process.env.ELECTRON_IS_DEV || !typedStore.get('settings').autoUpdate) {
    return;
  }

  autoUpdater.logger = log;
  autoUpdater.checkForUpdatesAndNotify();
}

// Initialize the app
app.whenReady().then(async () => {
  try {
    // Set application name
    app.setName('Storme Engine');
    
    // Initialize directories
    initializeDirectories();
    
    // Create the main window
    await createWindow();
    
    // Handle macOS activate event
    app.on('activate', async () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        await createWindow();
      } else if (mainWindow) {
        mainWindow.show();
      }
    });
    
    // Handle second instance
    app.on('second-instance', () => {
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.show();
        mainWindow.focus();
      }
    });
    
    log.info('Application started successfully');
  } catch (error) {
    log.error('Failed to start application:', error);
    dialog.showErrorBox('Startup Error', 'Failed to start Storme Engine. Please check the logs for more details.');
    app.quit();
  }
});

// Setup window controls
function setupWindowControls() {
  if (!mainWindow) return;
  
  // Handle keyboard shortcuts
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // Cmd/Ctrl + R to reload in dev
    if ((input.control || input.meta) && input.key.toLowerCase() === 'r') {
      if (process.env.ELECTRON_IS_DEV) {
        mainWindow?.reload();
      }
      event.preventDefault();
    }
    // Cmd/Ctrl + Q to quit
    else if ((input.control || input.meta) && input.key.toLowerCase() === 'q') {
      isQuitting = true;
      app.quit();
    }
  });
}

// Setup system tray
function setupTray() {
  if (tray || !mainWindow) return;
  
  try {
    // Use a default icon if the custom one doesn't exist
    let trayIcon: Electron.NativeImage;
    try {
      const iconPath = path.join(__dirname, '../renderer/assets/icons/tray.png');
      trayIcon = nativeImage.createFromPath(iconPath).resize({ width: 16, height: 16 });
    } catch (error) {
      // Fallback to a blank image if the icon is not found
      trayIcon = nativeImage.createEmpty();
    }
    
    tray = new Tray(trayIcon);
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Open Storme Engine',
        click: () => {
          if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.show();
            mainWindow.focus();
          } else {
            createWindow();
          }
        }
      },
      { type: 'separator' },
      {
        label: 'Check for Updates',
        click: () => {
          if (!process.env.ELECTRON_IS_DEV) {
            checkForUpdates();
          } else {
            dialog.showMessageBox({
              type: 'info',
              title: 'Development Mode',
              message: 'Auto-update is disabled in development mode.'
            });
          }
        }
      },
      { type: 'separator' },
      {
        label: 'Quit',
        click: () => {
          isQuitting = true;
          app.quit();
        }
      }
    ]);

    tray.setToolTip('Storme Engine');
    tray.setContextMenu(contextMenu);
    
    tray.on('click', () => {
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.show();
        mainWindow.focus();
      } else {
        createWindow();
      }
    });
  } catch (error) {
    log.error('Failed to setup system tray:', error);
  }
}

// Setup auto-updater
function setupAutoUpdater() {
  if (process.env.ELECTRON_IS_DEV) return;
  
  autoUpdater.autoDownload = true;
  autoUpdater.autoInstallOnAppQuit = true;
  autoUpdater.allowPrerelease = false;

  autoUpdater.on('checking-for-update', () => {
    log.info('Checking for updates...');
    mainWindow?.webContents.send('update-status', 'checking');
  });

  autoUpdater.on('update-available', (info: { version: string }) => {
    log.info('Update available:', info.version);
    mainWindow?.webContents.send('update-available', info);
  });

  autoUpdater.on('update-not-available', (info: { version: string }) => {
    log.info('No updates available');
    mainWindow?.webContents.send('update-not-available', info);
  });

  autoUpdater.on('download-progress', (progressObj: {
    bytesPerSecond: number;
    percent: number;
    transferred: number;
    total: number;
  }) => {
    mainWindow?.webContents.send('download-progress', progressObj);
  });

  autoUpdater.on('update-downloaded', (info: { version: string }) => {
    log.info('Update downloaded:', info.version);
    mainWindow?.webContents.send('update-downloaded', info);
    
    if (!mainWindow) return;
    
    // Notify user and ask to restart
    const showUpdateDialog = async () => {
      try {
        const messageBoxOptions: MessageBoxOptions = {
          type: 'info',
          buttons: ['Restart', 'Later'],
          title: 'Update Available',
          message: 'A new version has been downloaded. Restart the application to apply the updates.',
          detail: `Version ${info.version} is ready to install.`
        };
        
    const result: MessageBoxReturnValue = await dialog.showMessageBox(mainWindow!, messageBoxOptions);
        
        if (result.response === 0) { // 'Restart' button
          isQuitting = true;
          autoUpdater.quitAndInstall();
        }
      } catch (error) {
        log.error('Error showing update dialog:', error);
      }
    };
    
    showUpdateDialog();
  });

  autoUpdater.on('error', (error: Error) => {
    log.error('Update error:', error);
    mainWindow?.webContents.send('update-error', error.message);
  });
}

// Set up IPC handlers
function setupIpcHandlers() {
  // Window controls
  ipcMain.handle('window:minimize', () => {
    mainWindow?.minimize();
  });

  ipcMain.handle('window:maximize', () => {
    if (mainWindow?.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow?.maximize();
    }
  });

  ipcMain.handle('window:close', () => {
    mainWindow?.close();
  });

  // External links
  ipcMain.handle('open-external', (_, url: string) => {
    if (typeof url === 'string' && (url.startsWith('http://') || url.startsWith('https://'))) {
      shell.openExternal(url);
    }
  });
  
  // Settings
  ipcMain.handle('settings:get', (_, key: keyof AppConfig) => {
    return typedStore.get(key);
  });
  
  ipcMain.handle('settings:set', (_, key: keyof AppConfig, value: any) => {
    typedStore.set(key, value);
    return true;
  });
  
  // Auto-updater
  ipcMain.handle('updater:check', () => {
    if (!process.env.ELECTRON_IS_DEV) {
      return autoUpdater.checkForUpdatesAndNotify();
    }
    return Promise.resolve(null);
  });
  
  // File dialogs
  ipcMain.handle('dialog:openDirectory', async (): Promise<string | null> => {
    if (!mainWindow) return null;
    
    const result = await (dialog.showOpenDialog as (
      browserWindow: BrowserWindow,
      options: Electron.OpenDialogOptions
    ) => Promise<Electron.OpenDialogReturnValue>)(mainWindow, {
      properties: ['openDirectory']
    });
    
    return result.canceled || !result.filePaths.length ? null : result.filePaths[0];
  });

  // Game management
  ipcMain.handle('get-games', async () => {
    return []; // TODO: Implement game scanning
  });

  ipcMain.handle('launch-game', async (event: Electron.IpcMainInvokeEvent, gameId: string) => {
    // TODO: Implement game launching
    log.info(`Launching game ${gameId}`);
    return { success: true };
  });

  // Auth
  ipcMain.handle('login', async (event: Electron.IpcMainInvokeEvent, credentials: { username: string; password: string }) => {
    // TODO: Implement authentication
    log.info('Login attempt for user:', credentials.username);
    return { success: true, user: { id: 'user123', name: 'Test User' } };
  });
}

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Recreate window on macOS when dock icon is clicked
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  } else {
    mainWindow?.show();
  }
});

// Handle before quit
app.on('before-quit', () => {
  isQuitting = true;
});

// Auto-update events
autoUpdater.on('update-available', () => {
  log.info('Update available');
  mainWindow?.webContents.send('update-available');
});

autoUpdater.on('update-downloaded', () => {
  log.info('Update downloaded');
  mainWindow?.webContents.send('update-downloaded');
});

// Error handling
process.on('uncaughtException', (error) => {
  log.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
