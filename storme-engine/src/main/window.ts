import { BrowserWindow, BrowserWindowConstructorOptions, screen, app, ipcMain, shell } from 'electron';
import { join } from 'path';
import isDev from 'electron-is-dev';
import { format } from 'url';
import { setupContextMenu } from './context-menu';
import { createProtocol } from 'vue-cli-plugin-electron-builder/lib';

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
let mainWindow: BrowserWindow | null = null;
let forceQuit = false;

// Default window options
const defaultOptions: BrowserWindowConstructorOptions = {
  width: 1200,
  height: 800,
  minWidth: 800,
  minHeight: 600,
  show: false, // Don't show until ready-to-show
  frame: true, // Set to false for custom window controls
  titleBarStyle: 'hiddenInset', // Hide title bar (macOS)
  titleBarOverlay: {
    color: '#1e1e1e',
    symbolColor: '#ffffff',
    height: 30
  },
  webPreferences: {
    nodeIntegration: false,
    contextIsolation: true,
    sandbox: true,
    webSecurity: true,
    webviewTag: false,
    enableRemoteModule: false,
    preload: join(__dirname, '../preload/preload.js'),
    spellcheck: true,
    devTools: isDev,
  },
  backgroundColor: '#1e1e1e',
  icon: join(__dirname, '../../assets/icons/icon.png'),
};

/**
 * Create the main application window
 */
export function createWindow(): BrowserWindow {
  // Create the browser window with the specified options
  const window = new BrowserWindow({
    ...defaultOptions,
    ...getWindowPosition(),
  });

  // Set application menu
  setupContextMenu(window);

  // Load the app
  if (isDev) {
    // Load from webpack dev server in development
    window.loadURL('http://localhost:3000');
    
    // Open the DevTools in development mode
    window.webContents.openDevTools({ mode: 'detach' });
  } else {
    // Load the index.html file in production
    window.loadURL(
      format({
        pathname: join(__dirname, '../renderer/index.html'),
        protocol: 'file:',
        slashes: true,
      })
    );
  }

  // Emitted when the window is closed.
  window.on('closed', () => {
    // Dereference the window object
    mainWindow = null;
  });

  // Handle window close event
  window.on('close', (e) => {
    if (!forceQuit) {
      e.preventDefault();
      window.hide();
    }
  });

  // Handle new window event
  window.webContents.setWindowOpenHandler(({ url }) => {
    // Open external links in the default browser
    if (url.startsWith('http:') || url.startsWith('https:')) {
      shell.openExternal(url);
    }
    return { action: 'deny' }; // Prevent the creation of new windows
  });

  // Handle navigation events
  window.webContents.on('will-navigate', (event, url) => {
    // Prevent navigation to external sites
    if (!url.startsWith('http://localhost:3000') && !url.startsWith('file://')) {
      event.preventDefault();
      shell.openExternal(url);
    }
  });

  // Handle console messages
  window.webContents.on('console-message', (event, level, message, line, sourceId) => {
    const logLevels = ['debug', 'info', 'warning', 'error'];
    const logLevel = logLevels[level] || 'info';
    console[logLevel](`[Renderer] ${message} (${sourceId}:${line})`);
  });

  // Handle certificate errors
  window.webContents.session.setCertificateVerifyProc((request, callback) => {
    // Verify the certificate here
    const { hostname } = new URL(request.url);
    // For development, we'll allow all certificates
    callback(isDev ? 0 : -2); // -2 means reject
  });

  // Set application menu visibility based on platform
  if (process.platform === 'darwin') {
    window.setMenuBarVisibility(false);
  } else {
    window.setMenu(null);
  }

  // Set the main window reference
  mainWindow = window;
  
  return window;
}

/**
 * Get the main window instance
 */
export function getMainWindow(): BrowserWindow | null {
  return mainWindow;
}

/**
 * Show the main window
 */
export function showMainWindow() {
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.show();
    mainWindow.focus();
  }
}

/**
 * Toggle developer tools
 */
export function toggleDevTools() {
  if (mainWindow) {
    if (mainWindow.webContents.isDevToolsOpened()) {
      mainWindow.webContents.closeDevTools();
    } else {
      mainWindow.webContents.openDevTools({ mode: 'detach' });
    }
  }
}

/**
 * Get the optimal window position and size
 */
function getWindowPosition() {
  const display = screen.getPrimaryDisplay();
  const { width, height } = display.workAreaSize;
  
  // Calculate window size (80% of screen size)
  const windowWidth = Math.min(Math.floor(width * 0.8), 1600);
  const windowHeight = Math.min(Math.floor(height * 0.8), 1000);
  
  // Center the window
  const x = Math.floor((width - windowWidth) / 2);
  const y = Math.floor((height - windowHeight) / 2);
  
  return { width: windowWidth, height: windowHeight, x, y };
}

/**
 * Set the force quit flag
 */
export function setForceQuit(force: boolean) {
  forceQuit = force;
}

/**
 * Reload the main window
 */
export function reloadMainWindow() {
  if (mainWindow) {
    mainWindow.reload();
  }
}

/**
 * Toggle fullscreen mode
 */
export function toggleFullScreen() {
  if (mainWindow) {
    if (mainWindow.isFullScreen()) {
      mainWindow.setFullScreen(false);
    } else {
      mainWindow.setFullScreen(true);
    }
  }
}

/**
 * Toggle always on top
 */
export function toggleAlwaysOnTop() {
  if (mainWindow) {
    const isAlwaysOnTop = mainWindow.isAlwaysOnTop();
    mainWindow.setAlwaysOnTop(!isAlwaysOnTop);
    return !isAlwaysOnTop;
  }
  return false;
}

/**
 * Set window title
 */
export function setWindowTitle(title: string) {
  if (mainWindow) {
    mainWindow.setTitle(title);
  }
}

/**
 * Set window progress bar
 * @param progress 0 to 1, or -1 to remove progress bar
 */
export function setProgressBar(progress: number) {
  if (mainWindow) {
    if (progress >= 0 && progress <= 1) {
      mainWindow.setProgressBar(progress, { mode: 'normal' });
    } else if (progress > 1) {
      mainWindow.setProgressBar(progress, { mode: 'indeterminate' });
    } else {
      mainWindow.setProgressBar(-1);
    }
  }
}

// Handle window events
ipcMain.handle('window:minimize', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('window:maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('window:close', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

ipcMain.handle('window:reload', () => {
  reloadMainWindow();
});

ipcMain.handle('window:toggle-dev-tools', () => {
  toggleDevTools();
});

export default {
  createWindow,
  getMainWindow,
  showMainWindow,
  toggleDevTools,
  setForceQuit,
  reloadMainWindow,
  toggleFullScreen,
  toggleAlwaysOnTop,
  setWindowTitle,
  setProgressBar,
};
