// Core services
export * from './settings-service';
export * from './game-service';
export * from './update-service';
export * from './analytics-service';

// Re-export types
export * from '../../common/types/games';

// Initialize services
export function initializeServices(mainWindow: Electron.BrowserWindow) {
  // Initialize services that need the main window
  updateService.initialize(mainWindow);
  analyticsService.initialize(mainWindow);
  
  // Register IPC handlers
  settingsService.registerIpcHandlers();
  updateService.registerIpcHandlers();
  analyticsService.registerIpcHandlers();
  
  // Initialize game service with settings
  const defaultInstallPath = settingsService.getSetting<string>('app.defaultInstallPath');
  gameService.initialize(defaultInstallPath);
  
  console.log('All services initialized');
}
