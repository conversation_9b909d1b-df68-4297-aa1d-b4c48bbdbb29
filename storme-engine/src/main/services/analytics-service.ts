import { app, BrowserWindow, crashReporter } from 'electron';
import { v4 as uuidv4 } from 'uuid';
import { settingsService } from './settings-service';
import { IPC_CHANNELS } from '../../common/ipc-channels';
import { sendToRenderer } from '../ipc';
import path from 'path';
import fs from 'fs-extra';
import fetch from 'node-fetch';

// Types
type EventCategory = 'app' | 'game' | 'ui' | 'performance' | 'error' | 'settings' | 'update' | 'other';

export interface AnalyticsEvent {
  event: string;
  category: EventCategory;
  label?: string;
  value?: number;
  timestamp: number;
  sessionId: string;
  userId: string;
  properties?: Record<string, any>;
}

export interface CrashReport {
  date: string;
  platform: string;
  processType: string;
  version: string;
  productName: string;
  prod: string;
  _companyName: string;
  ver: string;
  crashReporterId: string;
  _productName: string;
  _version: string;
  _productVersion: string;
  uuid: string;
  _companyName_h: string;
  _productName_h: string;
  _version_h: string;
  _productVersion_h: string;
  upload_file_minidump: string;
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private sessionId: string = '';
  private userId: string = '';
  private analyticsEnabled: boolean = false;
  private crashReportingEnabled: boolean = false;
  private analyticsEndpoint: string = 'https://analytics.storme.engine/api/events';
  private crashEndpoint: string = 'https://crashes.storme.engine/api/crashes';
  private eventsQueue: AnalyticsEvent[] = [];
  private isSending: boolean = false;
  private flushInterval: NodeJS.Timeout | null = null;
  private mainWindow: BrowserWindow | null = null;
  private crashReportsPath: string = '';

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  public initialize(mainWindow: BrowserWindow): void {
    this.mainWindow = mainWindow;
    this.analyticsEnabled = settingsService.getSetting<boolean>('app.analytics');
    this.crashReportingEnabled = settingsService.getSetting<boolean>('app.crashReporting');
    this.crashReportsPath = path.join(app.getPath('userData'), 'Crash Reports');
    
    // Generate or load user ID
    this.loadOrCreateUserId();
    
    // Generate new session ID
    this.sessionId = uuidv4();
    
    // Set up crash reporting if enabled
    if (this.crashReportingEnabled) {
      this.setupCrashReporter();
    }
    
    // Set up periodic flush of events
    this.setupFlushInterval();
    
    // Track app start
    this.trackEvent('app_start', 'app');
    
    // Listen for settings changes
    settingsService.onSettingChanged((key: string, value: any) => {
      if (key === 'app.analytics') {
        this.analyticsEnabled = value;
        if (value) {
          this.setupFlushInterval();
        } else {
          this.clearFlushInterval();
        }
      }
      
      if (key === 'app.crashReporting') {
        this.crashReportingEnabled = value;
        if (value) {
          this.setupCrashReporter();
        }
      }
    });
    
    // Listen for app events
    app.on('will-quit', () => {
      this.trackEvent('app_quit', 'app');
      this.flushEvents();
    });
    
    app.on('window-all-closed', () => {
      this.trackEvent('all_windows_closed', 'app');
    });
    
    // Handle renderer process crashes
    mainWindow.webContents.on('render-process-gone', (event, details) => {
      this.handleRendererCrash(details);
    });
    
    mainWindow.webContents.on('unresponsive', () => {
      this.trackEvent('window_unresponsive', 'error', {
        windowId: mainWindow.id,
        url: mainWindow.webContents.getURL()
      });
    });
    
    mainWindow.on('unresponsive', () => {
      this.trackEvent('window_unresponsive', 'error', {
        windowId: mainWindow.id,
        url: mainWindow.webContents.getURL()
      });
    });
  }
  
  private loadOrCreateUserId(): void {
    const userIdPath = path.join(app.getPath('userData'), 'user_id');
    
    try {
      if (fs.existsSync(userIdPath)) {
        this.userId = fs.readFileSync(userIdPath, 'utf-8');
      } else {
        this.userId = uuidv4();
        fs.writeFileSync(userIdPath, this.userId, 'utf-8');
      }
    } catch (error) {
      console.error('Error loading/creating user ID:', error);
      // Fallback to a random ID if we can't read/write to disk
      this.userId = `anon-${Math.random().toString(36).substr(2, 9)}`;
    }
  }
  
  private setupCrashReporter(): void {
    if (!this.crashReportingEnabled) return;
    
    try {
      crashReporter.start({
        productName: 'Storme Engine',
        companyName: 'Storme',
        submitURL: this.crashEndpoint,
        uploadToServer: true,
        ignoreSystemCrashHandler: false,
        extra: {
          platform: process.platform,
          version: app.getVersion(),
          electron: process.versions.electron,
          chrome: process.versions.chrome,
          node: process.versions.node,
          v8: process.versions.v8,
          userId: this.userId
        }
      });
      
      console.log('Crash reporter initialized');
    } catch (error) {
      console.error('Failed to initialize crash reporter:', error);
    }
  }
  
  private setupFlushInterval(): void {
    if (!this.analyticsEnabled) return;
    
    // Clear any existing interval
    this.clearFlushInterval();
    
    // Set up new interval (every 30 seconds)
    this.flushInterval = setInterval(() => {
      this.flushEvents();
    }, 30 * 1000);
  }
  
  private clearFlushInterval(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
  }
  
  public trackEvent(
    event: string, 
    category: EventCategory, 
    properties: Record<string, any> = {}
  ): void {
    if (!this.analyticsEnabled) return;
    
    const analyticsEvent: AnalyticsEvent = {
      event,
      category,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      properties: {
        platform: process.platform,
        version: app.getVersion(),
        ...properties
      }
    };
    
    // Add to queue
    this.eventsQueue.push(analyticsEvent);
    
    // If queue is getting large, flush it
    if (this.eventsQueue.length >= 50) {
      this.flushEvents();
    }
  }
  
  public async flushEvents(): Promise<void> {
    if (this.eventsQueue.length === 0 || this.isSending) return;
    
    this.isSending = true;
    const eventsToSend = [...this.eventsQueue];
    this.eventsQueue = [];
    
    try {
      // In a real app, you would send this to your analytics service
      // For now, we'll just log it to the console in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Sending analytics events:', eventsToSend);
      }
      
      // Example of sending to a remote server
      // await fetch(this.analyticsEndpoint, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({ events: eventsToSend }),
      // });
      
    } catch (error) {
      console.error('Error sending analytics events:', error);
      
      // If there was an error, put the events back in the queue
      this.eventsQueue.unshift(...eventsToSend);
    } finally {
      this.isSending = false;
    }
  }
  
  public trackError(error: Error, context: Record<string, any> = {}): void {
    if (!this.analyticsEnabled && !this.crashReportingEnabled) return;
    
    const errorEvent: AnalyticsEvent = {
      event: 'error_occurred',
      category: 'error',
      label: error.name,
      value: 1,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      properties: {
        message: error.message,
        stack: error.stack,
        ...context
      }
    };
    
    // Add to queue
    this.eventsQueue.push(errorEvent);
    
    // If crash reporting is enabled, report the error
    if (this.crashReportingEnabled) {
      this.reportError(error, context);
    }
  }
  
  private reportError(error: Error, context: Record<string, any> = {}): void {
    if (!this.crashReportingEnabled) return;
    
    try {
      // In a real app, you would send this to your error tracking service
      // For now, we'll just log it to the console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error reported:', error, context);
      }
      
      // Example of sending to a remote server
      // await fetch(this.crashEndpoint, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     error: {
      //       name: error.name,
      //       message: error.message,
      //       stack: error.stack,
      //     },
      //     context,
      //     timestamp: new Date().toISOString(),
      //     platform: process.platform,
      //     version: app.getVersion(),
      //     userId: this.userId,
      //     sessionId: this.sessionId,
      //   }),
      // });
      
    } catch (reportError) {
      console.error('Error reporting error:', reportError);
    }
  }
  
  private handleRendererCrash(details: Electron.RenderProcessGoneDetails): void {
    const error = new Error(`Renderer process gone: ${details.reason}`);
    
    this.trackError(error, {
      type: 'renderer_crash',
      reason: details.reason,
      exitCode: details.exitCode,
      windowId: this.mainWindow?.id,
      url: this.mainWindow?.webContents.getURL()
    });
    
    // Try to recover or show an error message
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      dialog.showErrorBox(
        'Renderer Process Crashed',
        'The application has encountered an error and needs to reload. ' +
        'Please save your work and restart the application if the problem persists.'
      );
      
      // Try to reload the window
      try {
        this.mainWindow.webContents.reload();
      } catch (reloadError) {
        console.error('Failed to reload window after crash:', reloadError);
      }
    }
  }
  
  // IPC Handlers
  public registerIpcHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.ANALYTICS_TRACK, (_, event: string, category: EventCategory, properties?: Record<string, any>) => {
      this.trackEvent(event, category, properties);
    });
    
    ipcMain.handle(IPC_CHANNELS.ANALYTICS_ERROR, (_, error: Error, context?: Record<string, any>) => {
      this.trackError(error, context);
    });
    
    ipcMain.handle(IPC_CHANNELS.ANALYTICS_FLUSH, async () => {
      await this.flushEvents();
    });
  }
}

export const analyticsService = AnalyticsService.getInstance();
