import { app } from 'electron';
import path from 'path';
import fs from 'fs-extra';
import { IPC_CHANNELS } from '../../common/ipc-channels';
import { sendToRenderer } from '../ipc';

// Define the structure of the application settings
export interface AppSettings {
  // General settings
  app: {
    language: string;
    theme: 'light' | 'dark' | 'system';
    startMinimized: boolean;
    startWithOS: boolean;
    minimizeToTray: boolean;
    closeToTray: boolean;
    autoUpdate: boolean;
    betaUpdates: boolean;
    analytics: boolean;
    crashReporting: boolean;
    enableHardwareAcceleration: boolean;
    enableTrayIcon: boolean;
    defaultInstallPath: string;
  };

  // Game library settings
  library: {
    defaultPlatform: string;
    scanOnStartup: boolean;
    autoImportNewGames: boolean;
    showUninstalledGames: boolean;
    autoUpdateGames: boolean;
    updateCheckFrequency: 'hourly' | 'daily' | 'weekly';
    libraryPaths: string[];
    excludePaths: string[];
  };

  // Download settings
  downloads: {
    maxConcurrentDownloads: number;
    downloadSpeedLimit: number; // in KB/s, 0 = unlimited
    autoPauseOnNetworkMetered: boolean;
    deleteInstallersAfterInstall: boolean;
  };

  // Notifications settings
  notifications: {
    enableInAppNotifications: boolean;
    enableSound: boolean;
    enableDesktopNotifications: boolean;
    notifyOnGameLaunch: boolean;
    notifyOnGameUpdate: boolean;
    notifyOnDownloadComplete: boolean;
    notifyOnFriendOnline: boolean;
  };

  // Controller settings
  controller: {
    enableControllerSupport: boolean;
    controllerVibration: boolean;
    controllerDeadzone: number; // 0-1
    buttonMapping: Record<string, string>;
  };

  // Performance settings
  performance: {
    enableHardwareAcceleration: boolean;
    enableFrameRateLimit: boolean;
    maxFrameRate: number;
    disableComposition: boolean;
    disableDwm: boolean;
    disableGpuVsync: boolean;
    gpuAcceleration: 'auto' | 'opengl' | 'vulkan' | 'metal' | 'disabled';
  };

  // Cloud save settings
  cloud: {
    enableCloudSaves: boolean;
    autoSyncSaves: boolean;
    syncOnGameLaunch: boolean;
    syncOnGameExit: boolean;
    cloudStoragePath: string;
  };

  // Streaming settings
  streaming: {
    enableStreaming: boolean;
    streamingQuality: 'low' | 'medium' | 'high' | 'ultra';
    maxBitrate: number; // in Kbps
    enableHardwareEncoding: boolean;
    enableMicrophone: boolean;
    enableCamera: boolean;
    streamResolution: string;
    streamFps: number;
  };

  // Advanced settings
  advanced: {
    enableDebugMode: boolean;
    enableDevTools: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug' | 'silly';
    enableApiServer: boolean;
    apiPort: number;
    enableTelemetry: boolean;
    enableCrashReports: boolean;
    additionalArguments: string;
  };
}

// Default application settings
const DEFAULT_SETTINGS: AppSettings = {
  app: {
    language: 'en-US',
    theme: 'system',
    startMinimized: false,
    startWithOS: false,
    minimizeToTray: true,
    closeToTray: true,
    autoUpdate: true,
    betaUpdates: false,
    analytics: true,
    crashReporting: true,
    enableHardwareAcceleration: true,
    enableTrayIcon: true,
    defaultInstallPath: path.join(app.getPath('documents'), 'Storme Games')
  },
  
  library: {
    defaultPlatform: 'pc',
    scanOnStartup: true,
    autoImportNewGames: true,
    showUninstalledGames: true,
    autoUpdateGames: true,
    updateCheckFrequency: 'daily',
    libraryPaths: [],
    excludePaths: []
  },
  
  downloads: {
    maxConcurrentDownloads: 3,
    downloadSpeedLimit: 0, // Unlimited
    autoPauseOnNetworkMetered: true,
    deleteInstallersAfterInstall: true
  },
  
  notifications: {
    enableInAppNotifications: true,
    enableSound: true,
    enableDesktopNotifications: true,
    notifyOnGameLaunch: true,
    notifyOnGameUpdate: true,
    notifyOnDownloadComplete: true,
    notifyOnFriendOnline: true
  },
  
  controller: {
    enableControllerSupport: true,
    controllerVibration: true,
    controllerDeadzone: 0.15, // 15% deadzone
    buttonMapping: {}
  },
  
  performance: {
    enableHardwareAcceleration: true,
    enableFrameRateLimit: false,
    maxFrameRate: 60,
    disableComposition: false,
    disableDwm: false,
    disableGpuVsync: false,
    gpuAcceleration: 'auto'
  },
  
  cloud: {
    enableCloudSaves: true,
    autoSyncSaves: true,
    syncOnGameLaunch: true,
    syncOnGameExit: true,
    cloudStoragePath: path.join(app.getPath('userData'), 'cloud-saves')
  },
  
  streaming: {
    enableStreaming: false,
    streamingQuality: 'high',
    maxBitrate: 5000, // 5 Mbps
    enableHardwareEncoding: true,
    enableMicrophone: true,
    enableCamera: false,
    streamResolution: '1920x1080',
    streamFps: 60
  },
  
  advanced: {
    enableDebugMode: false,
    enableDevTools: false,
    logLevel: 'info',
    enableApiServer: false,
    apiPort: 8080,
    enableTelemetry: true,
    enableCrashReports: true,
    additionalArguments: ''
  }
};

export class SettingsService {
  private settings: AppSettings;
  private settingsPath: string;
  private isSaving: boolean = false;

  constructor() {
    this.settingsPath = path.join(app.getPath('userData'), 'settings.json');
    this.settings = this.loadSettings();
    this.ensureDirectories();
  }

  private ensureDirectories() {
    // Ensure all required directories exist
    const paths = [
      this.settings.app.defaultInstallPath,
      this.settings.cloud.cloudStoragePath,
      path.join(app.getPath('userData'), 'logs'),
      path.join(app.getPath('userData'), 'cache'),
      path.join(app.getPath('userData'), 'temp')
    ];

    paths.forEach(dir => {
      if (dir) {
        fs.ensureDirSync(dir);
      }
    });
  }

  private loadSettings(): AppSettings {
    try {
      if (fs.existsSync(this.settingsPath)) {
        const savedSettings = JSON.parse(fs.readFileSync(this.settingsPath, 'utf-8'));
        return this.mergeSettings(DEFAULT_SETTINGS, savedSettings);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // If there's an error, reset to default settings
      this.resetToDefault();
    }
    
    return { ...DEFAULT_SETTINGS };
  }

  private mergeSettings(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.mergeSettings(result[key] || {}, source[key]);
      } else if (source[key] !== undefined) {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  async saveSettings(): Promise<boolean> {
    if (this.isSaving) return false;
    this.isSaving = true;
    
    try {
      const settingsToSave = JSON.parse(JSON.stringify(this.settings));
      await fs.writeJson(this.settingsPath, settingsToSave, { spaces: 2 });
      this.notifySettingsChanged();
      return true;
    } catch (error) {
      console.error('Error saving settings:', error);
      return false;
    } finally {
      this.isSaving = false;
    }
  }

  getSettings(): AppSettings {
    return JSON.parse(JSON.stringify(this.settings));
  }

  getSetting<T>(path: string): T | undefined {
    const parts = path.split('.');
    let current: any = this.settings;
    
    for (const part of parts) {
      if (current[part] === undefined) {
        return undefined;
      }
      current = current[part];
    }
    
    return current as T;
  }

  async setSetting(path: string, value: any): Promise<boolean> {
    const parts = path.split('.');
    let current: any = this.settings;
    
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (current[part] === undefined) {
        current[part] = {};
      }
      current = current[part];
    }
    
    const lastPart = parts[parts.length - 1];
    if (current[lastPart] !== value) {
      current[lastPart] = value;
      return this.saveSettings();
    }
    
    return true;
  }

  async updateSettings(updates: Partial<AppSettings>): Promise<boolean> {
    let hasChanges = false;
    
    for (const [section, values] of Object.entries(updates)) {
      if (typeof values === 'object' && values !== null) {
        for (const [key, value] of Object.entries(values)) {
          const currentValue = this.settings[section as keyof AppSettings]?.[key as keyof AppSettings[keyof AppSettings]];
          if (JSON.stringify(currentValue) !== JSON.stringify(value)) {
            this.settings[section as keyof AppSettings] = {
              ...this.settings[section as keyof AppSettings],
              [key]: value
            } as any;
            hasChanges = true;
          }
        }
      }
    }
    
    if (hasChanges) {
      return this.saveSettings();
    }
    
    return true;
  }

  async resetToDefault(): Promise<boolean> {
    this.settings = JSON.parse(JSON.stringify(DEFAULT_SETTINGS));
    return this.saveSettings();
  }

  private notifySettingsChanged() {
    sendToRenderer(IPC_CHANNELS.SETTINGS_UPDATED, this.getSettings());
  }

  // IPC Handlers
  registerIpcHandlers() {
    ipcMain.handle(IPC_CHANNELS.SETTINGS_GET, (_, path?: string) => {
      if (path) {
        return this.getSetting(path);
      }
      return this.getSettings();
    });

    ipcMain.handle(IPC_CHANNELS.SETTINGS_SET, async (_, path: string, value: any) => {
      return this.setSetting(path, value);
    });

    ipcMain.handle(IPC_CHANNELS.SETTINGS_UPDATE, async (_, updates: Partial<AppSettings>) => {
      return this.updateSettings(updates);
    });

    ipcMain.handle(IPC_CHANNELS.SETTINGS_RESET, async () => {
      return this.resetToDefault();
    });
  }
}

export const settingsService = new SettingsService();
