import { app } from 'electron';
import path from 'path';
import fs from 'fs-extra';
import { v4 as uuidv4 } from 'uuid';
import { IPC_CHANNELS } from '../../common/ipc-channels';
import { sendToRenderer } from '../ipc';
import { Game, GameInstallationStatus, GamePlatform, GameUpdateInfo } from '../../common/types/games';

export class GameService {
  private games: Map<string, Game> = new Map();
  private gamesPath: string;
  private libraryPath: string;

  constructor() {
    this.gamesPath = path.join(app.getPath('userData'), 'games');
    this.libraryPath = path.join(app.getPath('userData'), 'library');
    this.ensureDirectories();
    this.loadGames();
  }

  private ensureDirectories() {
    fs.ensureDirSync(this.gamesPath);
    fs.ensureDirSync(this.libraryPath);
  }

  private getGamePath(gameId: string): string {
    return path.join(this.gamesPath, `${gameId}.json`);
  }

  private loadGames() {
    try {
      if (!fs.existsSync(this.gamesPath)) {
        return;
      }

      const gameFiles = fs.readdirSync(this.gamesPath).filter(file => file.endsWith('.json'));
      
      gameFiles.forEach(file => {
        try {
          const gameData = fs.readJsonSync(path.join(this.gamesPath, file));
          this.games.set(gameData.id, gameData);
        } catch (error) {
          console.error(`Error loading game from ${file}:`, error);
        }
      });
    } catch (error) {
      console.error('Error loading games:', error);
    }
  }

  private saveGame(game: Game) {
    try {
      const gamePath = this.getGamePath(game.id);
      fs.writeJsonSync(gamePath, game, { spaces: 2 });
      this.games.set(game.id, game);
      this.emitGamesUpdated();
      return true;
    } catch (error) {
      console.error('Error saving game:', error);
      return false;
    }
  }

  private emitGamesUpdated() {
    sendToRenderer(IPC_CHANNELS.GAME_LIBRARY_UPDATED, this.getAllGames());
  }

  async addGame(gameData: Omit<Game, 'id' | 'dateAdded' | 'lastPlayed' | 'status'>): Promise<Game> {
    const game: Game = {
      ...gameData,
      id: uuidv4(),
      dateAdded: new Date().toISOString(),
      lastPlayed: null,
      status: GameInstallationStatus.NOT_INSTALLED,
      installPath: gameData.installPath || path.join(this.libraryPath, gameData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase())
    };

    this.saveGame(game);
    return game;
  }

  async updateGame(gameId: string, updates: Partial<Game>): Promise<Game | null> {
    const game = this.games.get(gameId);
    if (!game) return null;

    const updatedGame = { ...game, ...updates };
    this.saveGame(updatedGame);
    return updatedGame;
  }

  async removeGame(gameId: string): Promise<boolean> {
    try {
      const game = this.games.get(gameId);
      if (!game) return false;

      // Remove game file
      const gamePath = this.getGamePath(gameId);
      if (fs.existsSync(gamePath)) {
        fs.unlinkSync(gamePath);
      }

      // Remove game directory if it exists
      if (game.installPath && fs.existsSync(game.installPath)) {
        fs.removeSync(game.installPath);
      }

      this.games.delete(gameId);
      this.emitGamesUpdated();
      return true;
    } catch (error) {
      console.error('Error removing game:', error);
      return false;
    }
  }

  getGame(gameId: string): Game | null {
    return this.games.get(gameId) || null;
  }

  getAllGames(): Game[] {
    return Array.from(this.games.values());
  }

  async installGame(gameId: string): Promise<boolean> {
    const game = this.games.get(gameId);
    if (!game) return false;

    try {
      await this.updateGame(gameId, { status: GameInstallationStatus.INSTALLING });
      
      // Simulate installation process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      await this.updateGame(gameId, { 
        status: GameInstallationStatus.INSTALLED,
        installPath: game.installPath,
        installDate: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      console.error('Error installing game:', error);
      await this.updateGame(gameId, { status: GameInstallationStatus.ERROR });
      return false;
    }
  }

  async uninstallGame(gameId: string): Promise<boolean> {
    const game = this.games.get(gameId);
    if (!game) return false;

    try {
      await this.updateGame(gameId, { status: GameInstallationStatus.UNINSTALLING });
      
      // Remove game files
      if (game.installPath && fs.existsSync(game.installPath)) {
        await fs.remove(game.installPath);
      }
      
      await this.updateGame(gameId, { 
        status: GameInstallationStatus.NOT_INSTALLED,
        installDate: undefined,
        lastPlayed: undefined
      });
      
      return true;
    } catch (error) {
      console.error('Error uninstalling game:', error);
      await this.updateGame(gameId, { status: GameInstallationStatus.ERROR });
      return false;
    }
  }

  async launchGame(gameId: string): Promise<boolean> {
    const game = this.games.get(gameId);
    if (!game || game.status !== GameInstallationStatus.INSTALLED) return false;

    try {
      await this.updateGame(gameId, { isRunning: true });
      
      // Simulate game launch
      console.log(`Launching game: ${game.title}`);
      
      // In a real implementation, this would launch the game executable
      // const { spawn } = require('child_process');
      // const gameProcess = spawn(game.executablePath, game.launchOptions || []);
      
      // Simulate game running
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await this.updateGame(gameId, { 
        isRunning: false,
        lastPlayed: new Date().toISOString(),
        playCount: (game.playCount || 0) + 1,
        totalPlayTime: (game.totalPlayTime || 0) + 5 // Add 5 minutes
      });
      
      return true;
    } catch (error) {
      console.error('Error launching game:', error);
      await this.updateGame(gameId, { isRunning: false });
      return false;
    }
  }

  async checkForUpdates(gameId: string): Promise<GameUpdateInfo | null> {
    const game = this.games.get(gameId);
    if (!game) return null;

    try {
      // Simulate update check
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 30% chance of update being available
      const updateAvailable = Math.random() < 0.3;
      
      if (updateAvailable) {
        return {
          available: true,
          currentVersion: game.version || '1.0.0',
          newVersion: '1.1.0',
          releaseNotes: '• Bug fixes and performance improvements\n• New features added\n• Enhanced stability',
          size: 1024 * 1024 * 150, // 150 MB
          isRequired: false
        };
      }
      
      return { available: false };
    } catch (error) {
      console.error('Error checking for updates:', error);
      return null;
    }
  }

  async updateGame(gameId: string): Promise<boolean> {
    const game = this.games.get(gameId);
    if (!game) return false;

    try {
      await this.updateGame(gameId, { isUpdating: true });
      
      // Simulate update download and installation
      console.log(`Updating game: ${game.title}`);
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await this.updateGame(gameId, { 
        version: '1.1.0',
        isUpdating: false,
        lastUpdated: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      console.error('Error updating game:', error);
      await this.updateGame(gameId, { isUpdating: false });
      return false;
    }
  }

  async scanForGames(): Promise<Game[]> {
    // Implement game scanning logic for different platforms
    const discoveredGames: Game[] = [];
    
    // Example: Scan common game installation directories
    const commonPaths = [
      path.join(app.getPath('home'), 'Games'),
      'C:\\Program Files',
      'C:\\Program Files (x86)',
      '/Applications',
      '~/Library/Application Support/Steam/steamapps/common',
      '~/.local/share/Steam/steamapps/common'
    ];

    // In a real implementation, this would scan the directories for game executables
    // and return discovered games
    
    return discoveredGames;
  }
}

export const gameService = new GameService();
