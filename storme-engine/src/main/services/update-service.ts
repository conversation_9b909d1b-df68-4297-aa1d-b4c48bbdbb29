import { autoUpdater, UpdateInfo } from 'electron-updater';
import { app, dialog, BrowserWindow } from 'electron';
import { IPC_CHANNELS } from '../../common/ipc-channels';
import { sendToRenderer } from '../ipc';
import { settingsService } from './settings-service';
import { UpdateDownloadedEvent, ProgressInfo } from 'builder-util-runtime';

export class UpdateService {
  private mainWindow: BrowserWindow | null = null;
  private updateAvailable: boolean = false;
  private updateInfo: UpdateInfo | null = null;
  private isCheckingForUpdates: boolean = false;
  private isDownloadingUpdate: boolean = false;
  private downloadProgress: number = 0;
  private lastCheckTime: number = 0;
  private updateCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.configureAutoUpdater();
  }

  public initialize(mainWindow: BrowserWindow): void {
    this.mainWindow = mainWindow;
    
    // Check for updates on startup if enabled
    if (settingsService.getSetting<boolean>('app.autoUpdate')) {
      this.checkForUpdates({ silent: true });
    }
    
    // Set up periodic update checks
    this.setupPeriodicUpdateCheck();
  }

  private configureAutoUpdater(): void {
    // Configure autoUpdater
    autoUpdater.autoDownload = false; // We'll handle downloads manually
    autoUpdater.allowPrerelease = settingsService.getSetting<boolean>('app.betaUpdates');
    autoUpdater.autoInstallOnAppQuit = true;
    autoUpdater.autoRunAppAfterInstall = true;
    autoUpdater.fullChangelog = true;
    
    // Set up event listeners
    autoUpdater.on('checking-for-update', () => {
      this.isCheckingForUpdates = true;
      this.updateAvailable = false;
      this.updateInfo = null;
      sendToRenderer(IPC_CHANNELS.UPDATE_CHECKING);
      
      if (this.mainWindow) {
        this.mainWindow.webContents.send(IPC_CHANNELS.UPDATE_CHECKING);
      }
    });
    
    autoUpdater.on('update-available', (info: UpdateInfo) => {
      console.log('Update available:', info.version);
      this.updateAvailable = true;
      this.updateInfo = info;
      this.isCheckingForUpdates = false;
      
      sendToRenderer(IPC_CHANNELS.UPDATE_AVAILABLE, {
        version: info.version,
        releaseNotes: info.releaseNotes,
        releaseDate: info.releaseDate,
        isPrerelease: info.prerelease
      });
      
      // If silent mode is off, show notification
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.showUpdateAvailableNotification(info);
      }
    });
    
    autoUpdater.on('update-not-available', () => {
      console.log('No updates available');
      this.updateAvailable = false;
      this.updateInfo = null;
      this.isCheckingForUpdates = false;
      
      sendToRenderer(IPC_CHANNELS.UPDATE_NOT_AVAILABLE);
    });
    
    autoUpdater.on('download-progress', (progress: ProgressInfo) => {
      this.downloadProgress = progress.percent;
      
      sendToRenderer(IPC_CHANNELS.UPDATE_DOWNLOAD_PROGRESS, {
        percent: progress.percent,
        bytesPerSecond: progress.bytesPerSecond,
        transferred: progress.transferred,
        total: progress.total
      });
    });
    
    autoUpdater.on('update-downloaded', (event: UpdateDownloadedEvent) => {
      console.log('Update downloaded, ready to install');
      this.isDownloadingUpdate = false;
      this.downloadProgress = 100;
      
      sendToRenderer(IPC_CHANNELS.UPDATE_DOWNLOADED, {
        version: event.version,
        releaseNotes: event.releaseNotes,
        releaseDate: event.releaseDate,
        isPrerelease: event.prerelease
      });
      
      // Show notification to the user
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.showUpdateReadyNotification(event);
      }
    });
    
    autoUpdater.on('error', (error: Error) => {
      console.error('Update error:', error);
      this.isCheckingForUpdates = false;
      this.isDownloadingUpdate = false;
      
      sendToRenderer(IPC_CHANNELS.UPDATE_ERROR, {
        message: error.message,
        stack: error.stack
      });
    });
  }
  
  private setupPeriodicUpdateCheck(): void {
    // Clear any existing interval
    if (this.updateCheckInterval) {
      clearInterval(this.updateCheckInterval);
    }
    
    // Check for updates every 6 hours if auto-update is enabled
    this.updateCheckInterval = setInterval(() => {
      if (settingsService.getSetting<boolean>('app.autoUpdate')) {
        this.checkForUpdates({ silent: true });
      }
    }, 6 * 60 * 60 * 1000); // 6 hours
  }
  
  public async checkForUpdates(options: { silent: boolean } = { silent: false }): Promise<boolean> {
    // Don't check if already checking
    if (this.isCheckingForUpdates || this.isDownloadingUpdate) {
      return false;
    }
    
    // Don't check more than once every 5 minutes
    const now = Date.now();
    if (now - this.lastCheckTime < 5 * 60 * 1000) {
      return false;
    }
    
    this.lastCheckTime = now;
    
    try {
      // Check for updates
      const result = await autoUpdater.checkForUpdates();
      
      if (result?.updateInfo) {
        this.updateAvailable = true;
        this.updateInfo = result.updateInfo;
        
        if (!options.silent && this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.showUpdateAvailableNotification(result.updateInfo);
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error checking for updates:', error);
      
      if (!options.silent && this.mainWindow && !this.mainWindow.isDestroyed()) {
        dialog.showErrorBox(
          'Update Error',
          'Failed to check for updates. Please check your internet connection and try again.'
        );
      }
      
      return false;
    }
  }
  
  public async downloadUpdate(): Promise<boolean> {
    if (!this.updateAvailable || this.isDownloadingUpdate) {
      return false;
    }
    
    try {
      this.isDownloadingUpdate = true;
      this.downloadProgress = 0;
      
      // Start the download
      await autoUpdater.downloadUpdate();
      
      return true;
    } catch (error) {
      console.error('Error downloading update:', error);
      this.isDownloadingUpdate = false;
      
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        dialog.showErrorBox(
          'Download Error',
          'Failed to download update. Please check your internet connection and try again.'
        );
      }
      
      return false;
    }
  }
  
  public async installUpdate(): Promise<void> {
    if (!this.updateAvailable) {
      return;
    }
    
    // Quit and install the update
    autoUpdater.quitAndInstall();
  }
  
  public getUpdateStatus() {
    return {
      isChecking: this.isCheckingForUpdates,
      isDownloading: this.isDownloadingUpdate,
      updateAvailable: this.updateAvailable,
      downloadProgress: this.downloadProgress,
      updateInfo: this.updateInfo
    };
  }
  
  private showUpdateAvailableNotification(updateInfo: UpdateInfo): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;
    
    const dialogOpts = {
      type: 'info' as const,
      buttons: ['Download', 'Later'],
      title: 'Update Available',
      message: `Version ${updateInfo.version} is available. Would you like to download it now?`,
      detail: updateInfo.releaseNotes?.toString() || 'Bug fixes and performance improvements.'
    };
    
    dialog.showMessageBox(this.mainWindow, dialogOpts).then((returnValue) => {
      if (returnValue.response === 0) {
        this.downloadUpdate();
      }
    });
  }
  
  private showUpdateReadyNotification(event: UpdateDownloadedEvent): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;
    
    const dialogOpts = {
      type: 'info' as const,
      buttons: ['Restart', 'Later'],
      title: 'Update Ready',
      message: 'A new version has been downloaded. Restart the application to apply the updates.',
      detail: `Version ${event.version} is ready to install.`
    };
    
    dialog.showMessageBox(this.mainWindow, dialogOpts).then((returnValue) => {
      if (returnValue.response === 0) {
        this.installUpdate();
      }
    });
  }
  
  // IPC Handlers
  public registerIpcHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.UPDATE_CHECK, async (_, options: { silent: boolean } = { silent: false }) => {
      return this.checkForUpdates(options);
    });
    
    ipcMain.handle(IPC_CHANNELS.UPDATE_DOWNLOAD, async () => {
      return this.downloadUpdate();
    });
    
    ipcMain.handle(IPC_CHANNELS.UPDATE_INSTALL, async () => {
      await this.installUpdate();
    });
    
    ipcMain.handle(IPC_CHANNELS.UPDATE_GET_STATUS, () => {
      return this.getUpdateStatus();
    });
  }
}

export const updateService = new UpdateService();
