{"root": true, "env": {"browser": true, "es2021": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "plugin:prettier/recommended", "next/core-web-vitals"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}, "project": "./tsconfig.eslint.json"}, "plugins": ["@typescript-eslint", "react", "react-hooks", "import", "prettier", "unused-imports"], "settings": {"react": {"version": "detect"}, "import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"], "moduleDirectory": ["node_modules", "src/"]}, "typescript": {"alwaysTryTypes": true}}}, "rules": {"prettier/prettier": ["warn", {}, {"usePrettierrc": true}], "react/prop-types": "off", "react/react-in-jsx-scope": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}, {"pattern": "@/**", "group": "internal"}, {"pattern": "@components/**", "group": "internal"}, {"pattern": "@pages/**", "group": "internal"}, {"pattern": "@hooks/**", "group": "internal"}, {"pattern": "@utils/**", "group": "internal"}, {"pattern": "@styles/**", "group": "internal"}, {"pattern": "@assets/**", "group": "internal"}, {"pattern": "@main/**", "group": "internal"}, {"pattern": "@common/**", "group": "internal"}], "pathGroupsExcludedImportTypes": ["react"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}]}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "rules": {"@typescript-eslint/explicit-function-return-type": ["warn"], "@typescript-eslint/explicit-module-boundary-types": ["warn"]}}, {"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-explicit-any": "off"}}]}