# Dependencies
node_modules
.next
out
dist
build
coverage

# Build files
.next/
out/
dist/
build/

# Cache
.cache/
.temp/
.tmp/

# Coverage
coverage/

# IDE
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env*.local

# Testing
**/__tests__/coverage/
**/__tests__/__mocks__/
**/__tests__/__fixtures__/

# Build artifacts
.next/
out/
dist/
build/

# Cache directories
.cache/
.temp/
.tmp/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Misc
*.pem
*.p12
*.key
*.mobileprovision
*.orig.*
