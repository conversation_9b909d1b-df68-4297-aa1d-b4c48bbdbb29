# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yaml,yml}]
indent_size = 2

# Shell scripts
[*.sh]
indent_style = tab
indent_size = 2

# Makefiles
[Makefile]
indent_style = tab

# Batch files
[*.{cmd,bat}]
end_of_line = crlf

# Windows PowerShell files
[*.ps1]
end_of_line = crlf
