# Browsers that we support

# Modern browsers with support for ES modules
modern
  and_chr >= 88
  and_ff >= 78
  and_uc >= 12.12
  android >= 81
  chrome >= 88
  chromeandroid >= 88
  edge >= 88
  firefox >= 78
  ios >= 12.2
  node >= 14.0.0
  opera >= 74
  safari >= 13.1
  samsung >= 13.0

# Defaults
> 0.5%
last 2 versions
not dead
not ie 11
not op_mini all

# Additional production targets
maintained node versions

# Development environment (used by babel-preset-react-app)
[development]
last 1 chrome version
last 1 firefox version
last 1 safari version

# Production environment (used by babel-preset-react-app)
[production]
>0.2%
not dead
not op_mini all

# Test environment (used by babel-preset-react-app)
[test]
current node
