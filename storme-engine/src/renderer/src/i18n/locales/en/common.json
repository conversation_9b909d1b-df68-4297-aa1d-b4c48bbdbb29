{"app": {"name": "Storme Engine", "description": "A powerful game development platform"}, "common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "search": "Search...", "noResults": "No results found", "noData": "No data available", "success": "Success!", "warning": "Warning", "info": "Information", "errorOccurred": "An error occurred", "pleaseWait": "Please wait...", "confirm": "Are you sure?", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "open": "Open", "settings": "Settings", "profile": "Profile", "signOut": "Sign Out", "signIn": "Sign In", "signUp": "Sign Up", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "postalCode": "Postal Code", "about": "About", "website": "Website", "social": "Social"}, "navigation": {"dashboard": "Dashboard", "games": "Games", "library": "Library", "marketplace": "Marketplace", "community": "Community", "documentation": "Documentation", "developer": "Developer", "studio": "Studio", "analytics": "Analytics", "monetization": "Monetization", "settings": "Settings", "help": "Help & Support"}, "auth": {"signInTitle": "Sign in to your account", "signUpTitle": "Create an account", "forgotPasswordTitle": "Reset your password", "resetPasswordTitle": "Set a new password", "rememberMe": "Remember me", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signInWith": "Or sign in with", "signUpWith": "Or sign up with", "forgotPasswordText": "Enter your email address and we'll send you a link to reset your password.", "resetPasswordText": "Enter your new password below.", "passwordResetSuccess": "Your password has been reset successfully.", "passwordResetError": "There was an error resetting your password.", "passwordResetEmailSent": "We've sent you an email with instructions to reset your password.", "passwordResetEmailError": "There was an error sending the password reset email.", "invalidCredentials": "Invalid email or password.", "accountLocked": "Your account has been locked due to too many failed login attempts. Please try again later.", "accountNotVerified": "Your account has not been verified. Please check your email for a verification link.", "resendVerificationEmail": "Resend verification email", "verificationEmailSent": "Verification email sent. Please check your inbox.", "verificationEmailError": "There was an error sending the verification email.", "signInSuccess": "You have successfully signed in.", "signUpSuccess": "Your account has been created successfully. Please check your email to verify your account.", "signUpError": "There was an error creating your account.", "signOutSuccess": "You have been signed out successfully.", "signOutError": "There was an error signing you out.", "sessionExpired": "Your session has expired. Please sign in again.", "unauthorized": "You are not authorized to access this page.", "forbidden": "You don't have permission to access this resource.", "notFound": "The requested resource was not found.", "serverError": "A server error occurred. Please try again later.", "networkError": "A network error occurred. Please check your internet connection and try again.", "unknownError": "An unknown error occurred. Please try again later."}, "validation": {"required": "{{field}} is required", "email": {"taken": "Email is already registered"}, "minLength": "{{field}} must be at least {{count}} characters", "maxLength": "{{field}} must be at most {{count}} characters", "minValue": "{{field}} must be at least {{count}}", "maxValue": "{{field}} must be at most {{count}}", "between": "{{field}} must be between {{min}} and {{max}}", "match": "{{field}} does not match", "password": {"minLength": "Password must be at least 8 characters", "uppercase": "Password must contain at least one uppercase letter", "lowercase": "Password must contain at least one lowercase letter", "number": "Password must contain at least one number", "special": "Password must contain at least one special character", "match": "Passwords do not match"}, "username": {"invalid": "Username can only contain letters, numbers, and underscores", "taken": "Username is already taken"}}, "dashboard": {"welcome": "Welcome back, {{name}}!", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "stats": {"games": "Games", "players": "Players", "sessions": "Sessions", "revenue": "Revenue"}, "noRecentActivity": "No recent activity"}, "games": {"title": "My Games", "createNew": "Create New Game", "noGames": "You don't have any games yet. Create your first game to get started!", "gameName": "Game Name", "gameDescription": "Description", "gameVersion": "Version", "gameStatus": "Status", "gameCreated": "Created", "gameUpdated": "Updated", "gameActions": "Actions", "status": {"draft": "Draft", "published": "Published", "archived": "Archived"}, "createGame": {"title": "Create New Game", "nameLabel": "Game Name", "namePlaceholder": "Enter a name for your game", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter a description for your game", "templateLabel": "Template", "templatePlaceholder": "Select a template", "createButton": "Create Game", "cancelButton": "Cancel"}, "deleteGame": {"title": "Delete Game", "message": "Are you sure you want to delete {{name}}? This action cannot be undone.", "deleteButton": "Delete", "cancelButton": "Cancel"}, "gameSettings": {"title": "Game Settings", "general": "General", "graphics": "Graphics", "audio": "Audio", "controls": "Controls", "saveButton": "Save Changes", "resetButton": "Reset to Defaults"}}, "editor": {"title": "Game Editor", "file": "File", "edit": "Edit", "view": "View", "window": "Window", "help": "Help", "newScene": "New Scene", "openScene": "Open Scene", "saveScene": "Save Scene", "saveAsScene": "Save Scene As...", "build": "Build", "buildAndRun": "Build & Run", "settings": "Settings", "exit": "Exit", "undo": "Undo", "redo": "Redo", "cut": "Cut", "copy": "Copy", "paste": "Paste", "delete": "Delete", "duplicate": "Duplicate", "selectAll": "Select All", "deselectAll": "Deselect All", "grid": "Show Grid", "snapToGrid": "Snap to Grid", "gridSize": "<PERSON><PERSON>", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetZoom": "Reset Zoom", "fullscreen": "Toggle Fullscreen", "panels": "Panels", "hierarchy": "Hierarchy", "inspector": "Inspector", "project": "Project", "console": "<PERSON><PERSON><PERSON>", "assets": "Assets", "animation": "Animation", "tilemap": "Tilemap", "tileset": "Tileset", "preferences": "Preferences", "keyboardShortcuts": "Keyboard Shortcuts", "documentation": "Documentation", "about": "About Storme Engine", "welcome": "Welcome to Storme Engine", "recentProjects": "Recent Projects", "openProject": "Open Project", "newProject": "New Project", "projectName": "Project Name", "projectLocation": "Location", "browse": "Browse...", "create": "Create", "cancel": "Cancel"}, "settings": {"title": "Settings", "general": "General", "appearance": "Appearance", "account": "Account", "notifications": "Notifications", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemMode": "System Default", "save": "Save Changes", "reset": "Reset to Defaults", "saved": "Setting<PERSON> saved successfully"}, "user": {"profile": "Profile", "settings": "Settings", "signOut": "Sign Out", "editProfile": "Edit Profile", "changePassword": "Change Password", "deleteAccount": "Delete Account", "profilePicture": "Profile Picture", "uploadPicture": "Upload Picture", "removePicture": "Remove Picture", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "socialProfiles": "Social Profiles", "saveChanges": "Save Changes", "cancel": "Cancel", "oldPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordUpdated": "Password updated successfully", "deleteAccountWarning": "This action cannot be undone. All your data will be permanently deleted.", "deleteAccountConfirm": "Type 'delete' to confirm:", "deleteAccountButton": "Delete My Account", "deleteAccountSuccess": "Your account has been deleted successfully"}}