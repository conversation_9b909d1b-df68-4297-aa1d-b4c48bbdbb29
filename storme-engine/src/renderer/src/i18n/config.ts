import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translations
import enCommon from './locales/en/common.json';
import esCommon from './locales/es/common.json';

// Types
export const defaultNS = 'common';
export const fallbackLng = 'en';
export const locales = ['en', 'es'] as const;

export type Locale = (typeof locales)[number];

export const resources = {
  en: {
    common: enCommon,
  },
  es: {
    common: esCommon,
  },
} as const;

declare module 'i18next' {
  interface CustomTypeOptions {
    defaultNS: typeof defaultNS;
    resources: {
      common: typeof enCommon;
    };
  }
}

i18n
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    // Debug only in development
    debug: process.env.NODE_ENV === 'development',
    
    // Default namespace
    defaultNS,
    fallbackLng,
    
    // Resources with translations
    resources,
    
    // Language detection options
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
    
    // Interpolation configuration
    interpolation: {
      escapeValue: false, // Not needed for React as it escapes by default
    },
    
    // React i18next options
    react: {
      useSuspense: true,
    },
  });

export default i18n;
