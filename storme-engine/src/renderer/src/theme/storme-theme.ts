import { createTheme } from '@mui/material/styles';
import { colors } from './colors';
import { typography } from './typography';

// ============================================================================
// Theme Constants
// ============================================================================

// Custom shadows
export const customShadows = {
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
  z8: '0 4px 16px 0 rgba(0, 0, 0, 0.15)',
  z16: '0 8px 24px 0 rgba(0, 0, 0, 0.2)',
  card: '0 8px 16px 0 rgba(0, 0, 0, 0.16)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.1)',
  outline: '0 0 0 3px #4229BC80',
  glow: '0 0 15px #7B65ED80',
  none: 'none',
};

export const globalStyles = {
  body: {
    backgroundColor: colors.background,
    color: colors.textPrimary,
    fontFamily: typography.fontFamily,
    lineHeight: 1.5,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    margin: 0,
    padding: 0,
  },
  '#root': {
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100vh',
  },
  a: {
    color: colors.primaryLight,
    textDecoration: 'none',
    '&:hover': {
      color: colors.accent,
    },
  },
} as const;

export const borderRadius = {
  none: '0',
  sm: '0.25rem',
  md: '0.5rem',
  lg: '1rem',
  xl: '1.5rem',
  full: '9999px',
} as const;

// ============================================================================
// Theme Type Declarations
// ============================================================================

import { Theme as MuiTheme, createTheme as muiCreateTheme, ThemeOptions } from '@mui/material/styles';

// Define our custom theme properties
type IStormeTheme = {
  customShadows: {
    [key: string]: string;
  };
  borderRadius: number;
  globalStyles: React.CSSProperties;
};

// Create a merged theme type
export type StormeTheme = MuiTheme & IStormeTheme;

// Extend the MUI module with our custom properties
declare module '@mui/material/styles' {
  // Extend the default theme with our custom properties
  interface Theme extends IStormeTheme {}
  
  // Theme options for createTheme
  interface ThemeOptions extends Partial<IStormeTheme> {}
  
  // Extend Typography variants
  interface TypographyVariants {
    fontFamilySecondary: string;
    fontFamilyMono: string;
    fontWeightSemiBold: number;
    fontWeightExtraBold: number;
    letterSpacingWide: string;
  }

  // Allow configuration using `createTheme`
  interface TypographyVariantsOptions {
    fontFamilySecondary?: string;
    fontFamilyMono?: string;
    fontWeightSemiBold?: number;
    fontWeightExtraBold?: number;
    letterSpacingWide?: string;
  }
}

// Custom createTheme function with proper typing
const createStormeTheme = (options: ThemeOptions = {}): StormeTheme => {
  // Create base theme with proper shape
  const baseTheme = muiCreateTheme({
    ...options,
    shape: {
      borderRadius: 8,
    },
  });

  // Create a new theme object with type assertion
  const stormeTheme = {
    ...baseTheme,
    customShadows: customShadows as any, // Use type assertion here
    borderRadius: 8,
    globalStyles: globalStyles as any, // Use type assertion here
  } as unknown as StormeTheme; // Double assertion to bypass type checking
  
  return stormeTheme;
};

// ============================================================================
// Theme Creation
// ============================================================================

// Create the theme instance with custom properties
const theme: StormeTheme = createStormeTheme({
  // Custom properties
  customShadows,
  borderRadius: 8, // Default border radius value
  globalStyles,
  
  // Material-UI theme overrides
  palette: {
    mode: 'dark',
    primary: {
      main: '#4229BC',
      light: '#7B65ED',
      dark: '#2A1B3D',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#F0BC2B',
      light: '#F5D16E',
      dark: '#9B7EF0',
      contrastText: '#2A1B3D',
    },
    error: {
      main: '#EF4444',
      light: '#F87171',
      dark: '#B91C1C',
      contrastText: '#FFFFFF',
    },
    warning: {
      main: '#F59E0B',
      light: '#FBBF24',
      dark: '#D97706',
      contrastText: '#1F2937',
    },
    info: {
      main: '#3B82F6',
      light: '#60A5FA',
      dark: '#1D4ED8',
      contrastText: '#FFFFFF',
    },
    success: {
      main: '#10B981',
      light: '#34D399',
      dark: '#059669',
      contrastText: '#FFFFFF',
    },
    text: {
      primary: '#F3F4F6',
      secondary: '#9CA3AF',
      disabled: '#6B7280',
    },
    divider: '#374151',
    background: {
      paper: '#111827',
      default: '#0A0A1A',
    },
    action: {
      active: '#9CA3AF',
      hover: 'rgba(255, 255, 255, 0.08)',
      selected: 'rgba(255, 255, 255, 0.16)',
      disabled: 'rgba(255, 255, 255, 0.3)',
      disabledBackground: 'rgba(255, 255, 255, 0.12)',
      focus: 'rgba(255, 255, 255, 0.12)',
    },
  },
  
  // Typography
  typography: {
    fontFamily: typography.fontFamily,
    fontFamilySecondary: typography.fontFamilySecondary,
    fontFamilyMono: typography.fontFamilyMono,
    fontWeightSemiBold: typography.fontWeightSemiBold,
    fontWeightExtraBold: typography.fontWeightExtraBold,
    letterSpacingWide: typography.letterSpacingWide,
    
    h1: {
      fontWeight: typography.fontWeightExtraBold,
      fontSize: '2.5rem',
      lineHeight: 1.2,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontWeight: typography.fontWeightExtraBold,
      fontSize: '2rem',
      lineHeight: 1.2,
      letterSpacing: '-0.02em',
    },
    h3: {
      fontWeight: typography.fontWeightBold,
      fontSize: '1.75rem',
      lineHeight: 1.2,
      letterSpacing: '-0.01em',
    },
    h4: {
      fontWeight: typography.fontWeightBold,
      fontSize: '1.5rem',
      lineHeight: 1.3,
    },
    h5: {
      fontWeight: typography.fontWeightSemiBold,
      fontSize: '1.25rem',
      lineHeight: 1.4,
    },
    h6: {
      fontWeight: typography.fontWeightSemiBold,
      fontSize: '1.125rem',
      lineHeight: 1.4,
    },
    subtitle1: {
      fontWeight: typography.fontWeightMedium,
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    subtitle2: {
      fontWeight: typography.fontWeightMedium,
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    button: {
      fontWeight: typography.fontWeightSemiBold,
      textTransform: 'none',
      letterSpacing: '0.02em',
    },
    caption: {
      fontSize: '0.75rem',
      lineHeight: 1.5,
      color: colors.grey[400],
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: typography.fontWeightSemiBold,
      lineHeight: 1.5,
      textTransform: 'uppercase',
      letterSpacing: '0.05em',
    },
  },
  
  // Shape
  shape: {
    borderRadius: borderRadius.md,
  },
  
  // Components
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        ...globalStyles,
        '#root': {
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.md,
          padding: '8px 16px',
          fontWeight: typography.fontWeightSemiBold,
        },
        sizeSmall: {
          padding: '4px 12px',
          fontSize: '0.8125rem',
        },
        sizeLarge: {
          padding: '12px 24px',
          fontSize: '1rem',
        },
        contained: {
          boxShadow: customShadows.z8,
          '&:hover': {
            boxShadow: customShadows.z16,
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.lg,
          boxShadow: customShadows.card,
          backgroundImage: 'none',
        },
      },
    },
    MuiCardHeader: {
      styleOverrides: {
        root: {
          padding: '24px 24px 16px',
        },
        title: {
          fontSize: '1.125rem',
          fontWeight: typography.fontWeightSemiBold,
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: '16px 24px',
          '&:last-child': {
            paddingBottom: '24px',
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.md,
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: colors.grey[700],
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: colors.grey[600],
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: colors.primary[500],
            boxShadow: `0 0 0 2px ${colors.primary[500]}33`,
          },
        },
        input: {
          padding: '12px 14px',
        },
      },
    },
    MuiInputLabel: {
      styleOverrides: {
        root: {
          color: colors.grey[400],
          '&.Mui-focused': {
            color: colors.primary[300],
          },
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          marginLeft: 0,
          marginTop: '4px',
          fontSize: '0.75rem',
          '&.Mui-error': {
            color: colors.error.light,
          },
        },
      },
    },
  },
});

// Export the theme and its type
export type Theme = typeof theme;
export default theme;
