import 'styled-components';
import { Theme as MuiTheme } from '@mui/material/styles';
import { colors } from './colors';
import { customShadows } from './storme-theme';
import { typography } from './typography';

// Define the complete theme type
type StormeTheme = Omit<MuiTheme, 'typography'> & {
  colors: typeof colors;
  customShadows: typeof customShadows;
  typography: typeof typography;
  borderRadius: {
    none: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
  };
  globalStyles: {
    body: React.CSSProperties;
    '#root': React.CSSProperties;
    a: React.CSSProperties & {
      '&:hover': React.CSSProperties;
    };
  };
  // Ensure all color properties are available at the root level
  primary: string;
  primaryLight: string;
  primaryLighter: string;
  primaryDark: string;
  accent: string;
  accentLight: string;
  accentDark: string;
  background: string;
  surface: string;
  surfaceLight: string;
  surfaceDark: string;
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  white: string;
  black: string;
  transparent: string;
  shadow: string;
  gradientPrimary: string;
  gradientDark: string;
  gradientAccent: string;
  overlay: string;
  backdrop: string;
  glow: string;
};

declare module 'styled-components' {
  export interface DefaultTheme extends StormeTheme {}
}

// Extend the MUI theme module
declare module '@mui/material/styles' {
  interface Theme extends StormeTheme {}
  interface ThemeOptions extends Partial<StormeTheme> {}
}
