import { createTheme, ThemeOptions } from '@mui/material/styles';
import { colors } from './colors';
import { typography } from './typography';

// Custom shadows
const customShadows = {
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.1)',
  outline: `0 0 0 3px ${colors.primary}80`,
  glow: `0 0 15px ${colors.primaryLight}80`,
  none: 'none',
} as const;

// Global styles
const globalStyles = {
  body: {
    backgroundColor: colors.background,
    color: colors.textPrimary,
    fontFamily: typography.fontFamily,
    lineHeight: 1.5,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    margin: 0,
    padding: 0,
  },
  '#root': {
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100vh',
  },
  a: {
    color: colors.primaryLight,
    textDecoration: 'none',
    '&:hover': {
      color: colors.accent,
    },
  },
} as const;

// Border radius
const borderRadius = {
  none: '0',
  sm: '0.25rem',
  md: '0.5rem',
  lg: '1rem',
  xl: '1.5rem',
  full: '9999px',
} as const;

// Define the theme options
const themeOptions: ThemeOptions = {
  palette: {
    mode: 'dark',
    primary: {
      main: colors.primary,
      light: colors.primaryLight,
      dark: colors.primaryDark,
      contrastText: colors.white,
    },
    secondary: {
      main: colors.accent,
      light: colors.accentLight,
      dark: colors.accentDark,
      contrastText: colors.primaryDark,
    },
    error: {
      main: colors.error,
      light: '#f87171',
      dark: '#dc2626',
      contrastText: colors.white,
    },
    warning: {
      main: colors.warning,
      light: '#fbbf24',
      dark: '#d97706',
      contrastText: colors.primaryDark,
    },
    info: {
      main: colors.info,
      light: '#60a5fa',
      dark: '#2563eb',
      contrastText: colors.white,
    },
    success: {
      main: colors.success,
      light: '#34d399',
      dark: '#059669',
      contrastText: colors.white,
    },
    text: {
      primary: colors.textPrimary,
      secondary: colors.textSecondary,
      disabled: colors.textDisabled,
    },
    background: {
      default: colors.background,
      paper: colors.surface,
    },
    divider: 'rgba(255, 255, 255, 0.12)',
  },
  typography,
  shape: {
    borderRadius: 8,
  },
  spacing: 8, // Base unit of 8px
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
  zIndex: {
    mobileStepper: 1000,
    speedDial: 1050,
    appBar: 1100,
    drawer: 1200,
    modal: 1300,
    snackbar: 1400,
    tooltip: 1500,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: globalStyles,
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 600,
          padding: '8px 24px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: customShadows.sm,
          },
        },
      },
    },
  },
};

// Create the theme
const theme = createTheme(themeOptions);

// Extend the theme with custom properties
declare module '@mui/material/styles' {
  interface Theme {
    customShadows: typeof customShadows;
    colors: typeof colors;
    borderRadius: typeof borderRadius;
    globalStyles: typeof globalStyles;
  }
  
  interface ThemeOptions {
    customShadows?: typeof customShadows;
    colors?: typeof colors;
    borderRadius?: typeof borderRadius;
    globalStyles?: typeof globalStyles;
  }
}

// Create the final theme with custom properties
const appTheme = {
  ...theme,
  customShadows,
  colors,
  borderRadius,
  globalStyles,
} as const;

export default appTheme;
