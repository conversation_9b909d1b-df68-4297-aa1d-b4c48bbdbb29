import 'styled-components';
import { Theme as MuiTheme } from '@mui/material/styles';

declare module 'styled-components' {
  export interface DefaultTheme extends MuiTheme {
    colors: {
      // Primary colors
      primary: string;
      primaryLight: string;
      primaryLighter: string;
      primaryDark: string;
      
      // Accent colors
      accent: string;
      accentLight: string;
      accentDark: string;
      
      // Background colors
      background: string;
      surface: string;
      surfaceLight: string;
      surfaceDark: string;
      
      // Text colors
      textPrimary: string;
      textSecondary: string;
      textTertiary: string;
      textDisabled: string;
      
      // Status colors
      success: string;
      warning: string;
      error: string;
      info: string;
      
      // Gradients
      gradientPrimary: string;
      gradientDark: string;
      gradientAccent: string;
      
      // Additional colors
      divider: string;
      overlay: string;
      backdrop: string;
      
      // Special effects
      glow: string;
      shadow: string;
    };
    
    typography: {
      fontFamily: string;
      fontFamilySecondary: string;
      fontFamilyMono: string;
      
      // Font weights
      fontWeightLight: number;
      fontWeightRegular: number;
      fontWeightMedium: number;
      fontWeightSemiBold: number;
      fontWeightBold: number;
      fontWeightExtraBold: number;
      
      // Font sizes
      h1: string;
      h2: string;
      h3: string;
      h4: string;
      h5: string;
      h6: string;
      body1: string;
      body2: string;
      caption: string;
      button: string;
      overline: string;
      
      // Line heights
      lineHeightTight: number;
      lineHeightNormal: number;
      lineHeightRelaxed: number;
      
      // Letter spacing
      letterSpacingTight: string;
      letterSpacingNormal: string;
      letterSpacingWide: string;
    };
    
    spacing: {
      xxs: string;
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      xxl: string;
      xxxl: string;
      xxxxl: string;
    };
    
    borderRadius: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      xxl: string;
      round: string;
      pill: string;
    };
    
    shadows: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
      inner: string;
      outline: string;
      glow: string;
      none: string;
      [key: string]: string;
    };
    
    transitions: {
      fast: string;
      normal: string;
      slow: string;
      bounce: string;
    };
    
    zIndex: {
      base: number;
      dropdown: number;
      sticky: number;
      fixed: number;
      overlay: number;
      modal: number;
      popover: number;
      toast: number;
      tooltip: number;
      max: number;
    };
    
    breakpoints: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      [key: string]: string;
    };
    
    globalStyles: {
      [key: string]: any;
    };
  }
}

// Extend the theme with our custom properties
declare module '@mui/material/styles' {
  interface Theme {
    custom: {
      colors: DefaultTheme['colors'];
      typography: DefaultTheme['typography'];
      spacing: DefaultTheme['spacing'];
      borderRadius: DefaultTheme['borderRadius'];
      shadows: DefaultTheme['shadows'];
      transitions: DefaultTheme['transitions'];
      zIndex: DefaultTheme['zIndex'];
      breakpoints: DefaultTheme['breakpoints'];
      globalStyles: DefaultTheme['globalStyles'];
    };
  }
  
  interface ThemeOptions {
    custom?: {
      colors?: Partial<DefaultTheme['colors']>;
      typography?: Partial<DefaultTheme['typography']>;
      spacing?: Partial<DefaultTheme['spacing']>;
      borderRadius?: Partial<DefaultTheme['borderRadius']>;
      shadows?: Partial<DefaultTheme['shadows']>;
      transitions?: Partial<DefaultTheme['transitions']>;
      zIndex?: Partial<DefaultTheme['zIndex']>;
      breakpoints?: Partial<DefaultTheme['breakpoints']>;
      globalStyles?: Partial<DefaultTheme['globalStyles']>;
    };
  }
}
