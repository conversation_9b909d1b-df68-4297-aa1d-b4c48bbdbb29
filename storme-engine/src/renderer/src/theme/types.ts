import { Theme as MuiTheme, ThemeOptions as MuiThemeOptions } from '@mui/material/styles';
import { colors } from './colors';
import { customShadows } from './storme-theme';
import { typography } from './typography';

export interface StormeTheme extends Omit<MuiTheme, 'typography'> {
  colors: typeof colors;
  customShadows: typeof customShadows;
  typography: typeof typography;
  borderRadius: {
    none: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
  };
  globalStyles: {
    body: React.CSSProperties;
    '#root': React.CSSProperties;
    a: React.CSSProperties & {
      '&:hover': React.CSSProperties;
    };
  };
  // Color properties available at root level
  primary: string;
  primaryLight: string;
  primaryLighter: string;
  primaryDark: string;
  accent: string;
  accentLight: string;
  accentDark: string;
  background: string;
  surface: string;
  surfaceLight: string;
  surfaceDark: string;
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  white: string;
  black: string;
  transparent: string;
  shadow: string;
  gradientPrimary: string;
  gradientDark: string;
  gradientAccent: string;
  overlay: string;
  backdrop: string;
  glow: string;
  divider: string;
}

export interface StormeThemeOptions extends Omit<MuiThemeOptions, 'typography'> {
  colors?: Partial<typeof colors>;
  customShadows?: Partial<typeof customShadows>;
  typography?: Partial<typeof typography>;
  borderRadius?: Partial<StormeTheme['borderRadius']>;
  globalStyles?: Partial<StormeTheme['globalStyles']>;
  // Add any other theme options here
}

// Extend the MUI theme module
declare module '@mui/material/styles' {
  interface Theme extends StormeTheme {}
  interface ThemeOptions extends StormeThemeOptions {}
}

// Extend styled-components module
declare module 'styled-components' {
  export interface DefaultTheme extends StormeTheme {}
}
