import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import App from './App';
import './styles/global.css';

// Initialize the application
function initialize() {
  const root = createRoot(document.getElementById('root')!);
  
  root.render(
    <React.StrictMode>
      <Router>
        <App />
      </Router>
    </React.StrictMode>
  );
}

// Start the application
initialize();
