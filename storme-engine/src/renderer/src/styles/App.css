/* App Layout */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-primary);
}

/* Header */
.app-header {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.nav-logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.nav-logo:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  background-color: rgba(108, 92, 231, 0.1);
  text-decoration: none;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: var(--spacing-lg) 0;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg);
}

/* Footer */
.app-footer {
  background-color: var(--surface-color);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Home Page */
.home-page {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-md);
}

.home-page h1 {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  line-height: 1.2;
}

.home-page p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

.quick-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-xxl);
}

.recent-games {
  margin-top: var(--spacing-xxl);
  text-align: left;
}

.recent-games h2 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  position: relative;
  display: inline-block;
}

.recent-games h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius-full);
}

.game-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.game-card {
  background-color: var(--card-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  cursor: pointer;
  text-decoration: none;
  color: var(--text-primary);
  text-align: left;
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.game-cover {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.game-cover-placeholder {
  width: 100%;
  height: 250px;
  background: linear-gradient(135deg, var(--surface-color), var(--card-color));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-secondary);
}

.game-info {
  padding: var(--spacing-md);
}

.game-info h3 {
  font-size: 1.1rem;
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.game-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.game-info small {
  font-size: 0.75rem;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* Game Details */
.game-details {
  max-width: 1200px;
  margin: 0 auto;
  color: var(--text-primary);
}

.game-header {
  position: relative;
  height: 400px;
  background-size: cover;
  background-position: center;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.game-header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.8));
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: var(--spacing-xl);
}

.game-header-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.game-title-container h1 {
  font-size: 3rem;
  margin-bottom: var(--spacing-sm);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 0.9375rem;
}

.game-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.btn-play {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  background-color: var(--primary-color);
  color: white;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-play:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-play:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-back {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: var(--spacing-lg);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) 0;
  transition: color var(--transition-fast);
}

.btn-back:hover {
  color: var(--primary-color);
}

.game-content {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.game-tabs {
  display: flex;
  gap: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-sm);
}

.tab {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.9375rem;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  position: relative;
  transition: color var(--transition-fast);
}

.tab:hover {
  color: var(--primary-color);
}

.tab.active {
  color: var(--primary-color);
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: calc(-1 * var(--spacing-sm) - 1px);
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: var(--border-radius-full);
}

.tab-content {
  line-height: 1.7;
}

.game-description {
  margin-bottom: var(--spacing-xl);
}

.game-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.info-item {
  background-color: var(--card-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
}

.info-label {
  display: block;
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.info-value {
  font-weight: 500;
  color: var(--text-primary);
}

.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.screenshot-item {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-normal);
}

.screenshot-item:hover {
  transform: translateY(-4px);
}

.screenshot-item img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.screenshot-item:hover img {
  transform: scale(1.05);
}

/* Settings Page */
.settings-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.settings-container {
  display: flex;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.settings-sidebar {
  width: 240px;
  flex-shrink: 0;
}

.sidebar-item {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.9375rem;
  font-weight: 500;
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.sidebar-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--primary-color);
}

.sidebar-item.active {
  background-color: var(--primary-color);
  color: white;
}

.settings-content {
  flex: 1;
  background-color: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.settings-section h2 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.path-selector {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.path-selector input {
  flex: 1;
  background-color: var(--card-color);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
}

.hint {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  margin-bottom: 0;
}

.range-value {
  text-align: center;
  font-weight: 500;
  margin-top: var(--spacing-xs);
}

.about-info {
  line-height: 1.7;
}

.about-links {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  align-items: center;
}

.about-links a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.about-links a:hover {
  text-decoration: underline;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* Restart Prompt */
.restart-prompt {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.restart-content {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  max-width: 400px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  text-align: center;
}

.restart-content h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.restart-content p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
}

.restart-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* 404 Page */
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
  text-align: center;
  padding: var(--spacing-xl);
}

.not-found-content h1 {
  font-size: 6rem;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  line-height: 1;
}

.not-found-content h2 {
  font-size: 2rem;
  margin: var(--spacing-md) 0 var(--spacing-lg);
  color: var(--text-primary);
}

.not-found-content p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .settings-container {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
  }
  
  .sidebar-item {
    white-space: nowrap;
  }
  
  .game-header {
    height: 300px;
  }
  
  .game-title-container h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 var(--spacing-sm);
  }
  
  .nav {
    padding: 0;
  }
  
  .nav-links {
    display: none;
  }
  
  .game-header {
    height: 250px;
  }
  
  .game-title-container h1 {
    font-size: 1.75rem;
  }
  
  .game-meta {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
  
  .game-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .btn-play {
    width: 100%;
  }
  
  .quick-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .quick-actions .btn {
    width: 100%;
  }
}
