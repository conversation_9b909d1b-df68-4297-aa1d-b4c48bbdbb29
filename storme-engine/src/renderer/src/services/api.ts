// API Service for Storme Engine Desktop App
import axios, { AxiosInstance, AxiosResponse } from 'axios';

const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api.gamestorme.com' 
  : 'http://localhost:3001';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface User {
  id: string;
  email: string;
  displayName: string;
  userType: 'gamer' | 'developer';
  createdAt: string;
  lastLogin: string | null;
  profile: {
    avatar: string | null;
    level: number;
    experience: number;
    gamesPlayed: number;
    totalPlaytime: number;
    achievements: any[];
  };
}

interface Game {
  id: string;
  title: string;
  description: string;
  image: string;
  genre: string[];
  rating: number;
  price: number;
  isFree: boolean;
  isInstalled: boolean;
  isFavorite: boolean;
  developer: string;
  releaseDate: string;
  size: string;
  version: string;
  platforms: string[];
  screenshots: string[];
  systemRequirements: {
    minimum: {
      os: string;
      processor: string;
      memory: string;
      graphics: string;
      storage: string;
    };
  };
}

interface AuthResponse {
  user: User;
  token: string;
  message: string;
}

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: `${API_BASE_URL}/api`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.clearToken();
          // Redirect to login or emit auth error event
          window.dispatchEvent(new CustomEvent('auth-error'));
        }
        return Promise.reject(error);
      }
    );

    // Load token from localStorage
    this.loadToken();
  }

  private loadToken(): void {
    try {
      const savedToken = localStorage.getItem('storme_auth_token');
      if (savedToken) {
        this.token = savedToken;
      }
    } catch (error) {
      console.error('Failed to load token:', error);
    }
  }

  private saveToken(token: string): void {
    try {
      this.token = token;
      localStorage.setItem('storme_auth_token', token);
    } catch (error) {
      console.error('Failed to save token:', error);
    }
  }

  private clearToken(): void {
    this.token = null;
    localStorage.removeItem('storme_auth_token');
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/health');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Authentication methods
  async register(email: string, password: string, displayName: string, userType: 'gamer' | 'developer' = 'gamer'): Promise<AuthResponse> {
    try {
      const response = await this.api.post('/auth/register', {
        email,
        password,
        displayName,
        userType,
      });
      
      const { token } = response.data;
      this.saveToken(token);
      
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await this.api.post('/auth/login', {
        email,
        password,
      });
      
      const { token } = response.data;
      this.saveToken(token);
      
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async logout(): Promise<ApiResponse> {
    try {
      const response = await this.api.post('/auth/logout');
      this.clearToken();
      return response.data;
    } catch (error) {
      this.clearToken();
      throw this.handleError(error);
    }
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await this.api.get('/auth/me');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Games methods
  async getGames(params?: {
    search?: string;
    genre?: string;
    platform?: string;
    status?: 'installed' | 'favorites' | 'free';
    limit?: number;
    page?: number;
  }): Promise<PaginatedResponse<Game>> {
    try {
      const response = await this.api.get('/games', { params });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getGame(id: string): Promise<ApiResponse<Game>> {
    try {
      const response = await this.api.get(`/games/${id}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async installGame(id: string): Promise<ApiResponse<Game>> {
    try {
      const response = await this.api.post(`/games/${id}/install`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async uninstallGame(id: string): Promise<ApiResponse<Game>> {
    try {
      const response = await this.api.post(`/games/${id}/uninstall`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async toggleFavorite(id: string): Promise<ApiResponse<Game>> {
    try {
      const response = await this.api.post(`/games/${id}/favorite`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Dashboard methods
  async getUserDashboard(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/user/dashboard');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getUserStats(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/user/stats');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getDeveloperDashboard(): Promise<ApiResponse> {
    try {
      const response = await this.api.get('/developer/dashboard');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token;
  }

  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.error || error.response.data?.message || 'Server error';
      return new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - please check your connection');
    } else {
      // Something else happened
      return new Error(error.message || 'An unexpected error occurred');
    }
  }
}

// Create singleton instance
const apiService = new ApiService();

export default apiService;
export type { User, Game, ApiResponse, PaginatedResponse, AuthResponse };
