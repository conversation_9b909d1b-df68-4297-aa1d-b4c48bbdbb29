import React from 'react';
import { Outlet, Link } from 'react-router-dom';

const MainLayout: React.FC = () => {
  return (
    <div className="app-container">
      <header className="app-header">
        <nav className="nav">
          <Link to="/" className="nav-logo">Storme Engine</Link>
          <div className="nav-links">
            <Link to="/" className="nav-link">Home</Link>
            <Link to="/library" className="nav-link">Library</Link>
            <Link to="/settings" className="nav-link">Settings</Link>
          </div>
        </nav>
      </header>
      <main className="app-main">
        <Outlet />
      </main>
      <footer className="app-footer">
        <p>© {new Date().getFullYear()} Storme Engine</p>
      </footer>
    </div>
  );
};

export default MainLayout;
