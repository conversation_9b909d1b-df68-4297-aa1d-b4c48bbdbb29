import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { I18nextProvider } from 'react-i18next';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { HelmetProvider } from 'react-helmet-async';

// Local imports
import App from './App';
import { AppProvider } from './contexts/AppContext';
import theme, { Theme } from './theme';
import { GlobalStyle } from './styles';
import i18n from './i18n/config';

declare module 'styled-components' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  export interface DefaultTheme extends Theme {}
}

// Initialize Query Client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Get the root element
const container = document.getElementById('root');
if (!container) throw new Error('Failed to find the root element');
const root = createRoot(container);

// Create a theme instance that works with both MUI and styled-components
const appTheme: Theme = {
  ...theme,
  // Include all color properties at the root level for styled-components
  ...(theme.palette as any),
  // Include other theme properties
  typography: theme.typography,
  shape: theme.shape,
  spacing: theme.spacing,
  breakpoints: theme.breakpoints,
  zIndex: theme.zIndex,
  // Border radius values
  borderRadius: theme.borderRadius,
  // Custom shadows
  customShadows: theme.customShadows,
  // Global styles
  globalStyles: theme.globalStyles
};

root.render(
  <React.StrictMode>
    <HelmetProvider>
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
          <I18nextProvider i18n={i18n}>
            <MuiThemeProvider theme={theme}>
              <StyledThemeProvider theme={appTheme}>
                <GlobalStyle />
                <AppProvider>
                  <App />
                </AppProvider>
                {process.env.NODE_ENV === 'development' && (
                  <ReactQueryDevtools initialIsOpen={false} position="bottom-right" />
                )}
              </StyledThemeProvider>
            </MuiThemeProvider>
          </I18nextProvider>
        </QueryClientProvider>
      </BrowserRouter>
    </HelmetProvider>
  </React.StrictMode>
);

// Handle errors in the renderer process
window.addEventListener('error', (event) => {
  console.error('Uncaught error:', event.error);
  // You can add error reporting here (e.g., Sentry, LogRocket)
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled rejection:', event.reason);
  // You can add error reporting here
});
