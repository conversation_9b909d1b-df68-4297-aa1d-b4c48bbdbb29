import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Tabs,
  Tab,
  Container
} from '@mui/material';
import {
  Search as SearchIcon,
  PlayArrow as PlayIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Download as DownloadIcon,
  Star as StarIcon
} from '@mui/icons-material';
import styled from 'styled-components';
import { GameLoadingScreen } from '../components/common/GameLoadingScreen';

const StyledContainer = styled(Container)`
  padding: 2rem 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
`;

const GameCard = styled(Card)`
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.3);
    border-color: rgba(255, 87, 34, 0.5);
  }
`;

const GameImage = styled(CardMedia)`
  height: 200px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  }
`;

const PlayButton = styled(Button)`
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: rgba(255, 87, 34, 0.9) !important;
  color: white !important;
  border-radius: 50% !important;
  width: 60px !important;
  height: 60px !important;
  min-width: 60px !important;
  opacity: 0;
  transition: all 0.3s ease !important;
  z-index: 2;

  ${GameCard}:hover & {
    opacity: 1;
  }

  &:hover {
    background: rgba(255, 87, 34, 1) !important;
    transform: translate(-50%, -50%) scale(1.1) !important;
  }
`;

const GameTitle = styled(Typography)`
  color: white !important;
  font-weight: bold !important;
  margin-bottom: 0.5rem !important;
`;

const GameDescription = styled(Typography)`
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 0.9rem !important;
  margin-bottom: 1rem !important;
`;

const GameGenre = styled(Chip)`
  background: rgba(255, 87, 34, 0.2) !important;
  color: #ff5722 !important;
  border: 1px solid rgba(255, 87, 34, 0.3) !important;
  margin-right: 0.5rem !important;
  margin-bottom: 0.5rem !important;
`;

const SearchField = styled(TextField)`
  .MuiOutlinedInput-root {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 25px;

    fieldset {
      border-color: rgba(255, 255, 255, 0.2);
    }

    &:hover fieldset {
      border-color: rgba(255, 87, 34, 0.5);
    }

    &.Mui-focused fieldset {
      border-color: #ff5722;
    }

    input {
      color: white;
    }
  }
`;

const StyledTabs = styled(Tabs)`
  .MuiTabs-indicator {
    background-color: #ff5722;
  }

  .MuiTab-root {
    color: rgba(255, 255, 255, 0.7);

    &.Mui-selected {
      color: #ff5722;
    }
  }
`;

interface Game {
  id: string;
  title: string;
  description: string;
  image: string;
  genre: string[];
  rating: number;
  price: number;
  isFree: boolean;
  isInstalled: boolean;
  isFavorite: boolean;
}

const mockGames: Game[] = [
  {
    id: '1',
    title: 'Cyber Legends',
    description: 'An epic cyberpunk adventure in a neon-lit future city.',
    image: '/api/placeholder/300/200',
    genre: ['Action', 'RPG'],
    rating: 4.8,
    price: 29.99,
    isFree: false,
    isInstalled: true,
    isFavorite: false
  },
  {
    id: '2',
    title: 'Space Explorer',
    description: 'Explore the vast universe and discover new worlds.',
    image: '/api/placeholder/300/200',
    genre: ['Adventure', 'Simulation'],
    rating: 4.6,
    price: 0,
    isFree: true,
    isInstalled: false,
    isFavorite: true
  },
  {
    id: '3',
    title: 'Medieval Kingdoms',
    description: 'Build your empire in this strategic medieval game.',
    image: '/api/placeholder/300/200',
    genre: ['Strategy', 'Simulation'],
    rating: 4.7,
    price: 39.99,
    isFree: false,
    isInstalled: true,
    isFavorite: false
  },
  {
    id: '4',
    title: 'Racing Thunder',
    description: 'High-speed racing with stunning graphics.',
    image: '/api/placeholder/300/200',
    genre: ['Racing', 'Sports'],
    rating: 4.5,
    price: 19.99,
    isFree: false,
    isInstalled: false,
    isFavorite: true
  }
];

export const GamesPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [games, setGames] = useState<Game[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState(0);

  useEffect(() => {
    // Simulate loading games data
    const timer = setTimeout(() => {
      setGames(mockGames);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleLoadingComplete = () => {
    setLoading(false);
  };

  const handlePlayGame = (game: Game) => {
    console.log('Playing game:', game.title);
    // Add game launch logic here
  };

  const handleDownloadGame = (game: Game) => {
    console.log('Downloading game:', game.title);
    // Add download logic here
  };

  const toggleFavorite = (gameId: string) => {
    setGames(prev => prev.map(game =>
      game.id === gameId ? { ...game, isFavorite: !game.isFavorite } : game
    ));
  };

  const filteredGames = games.filter(game => {
    const matchesSearch = game.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         game.description.toLowerCase().includes(searchTerm.toLowerCase());

    switch (selectedTab) {
      case 1: // Installed
        return matchesSearch && game.isInstalled;
      case 2: // Favorites
        return matchesSearch && game.isFavorite;
      case 3: // Free
        return matchesSearch && game.isFree;
      default: // All
        return matchesSearch;
    }
  });

  if (loading) {
    return <GameLoadingScreen onComplete={handleLoadingComplete} />;
  }

  return (
    <StyledContainer maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ color: 'white', fontWeight: 'bold' }}>
          Game Library
        </Typography>
        <Typography variant="h6" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 3 }}>
          Discover and play amazing games
        </Typography>

        <Box display="flex" gap={2} mb={3} flexWrap="wrap">
          <SearchField
            placeholder="Search games..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: 'rgba(255, 255, 255, 0.5)' }} />
                </InputAdornment>
              ),
            }}
            sx={{ flexGrow: 1, minWidth: 300 }}
          />
        </Box>

        <StyledTabs
          value={selectedTab}
          onChange={(_, newValue) => setSelectedTab(newValue)}
          sx={{ mb: 3 }}
        >
          <Tab label="All Games" />
          <Tab label="Installed" />
          <Tab label="Favorites" />
          <Tab label="Free" />
        </StyledTabs>
      </Box>

      <Grid container spacing={3}>
        {filteredGames.map((game) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={game.id}>
            <GameCard>
              <Box position="relative">
                <GameImage
                  image={game.image}
                  title={game.title}
                />
                <PlayButton
                  onClick={() => game.isInstalled ? handlePlayGame(game) : handleDownloadGame(game)}
                >
                  {game.isInstalled ? <PlayIcon /> : <DownloadIcon />}
                </PlayButton>
              </Box>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                  <GameTitle variant="h6">
                    {game.title}
                  </GameTitle>
                  <IconButton
                    size="small"
                    onClick={() => toggleFavorite(game.id)}
                    sx={{ color: game.isFavorite ? '#ff5722' : 'rgba(255, 255, 255, 0.5)' }}
                  >
                    {game.isFavorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                  </IconButton>
                </Box>

                <GameDescription variant="body2">
                  {game.description}
                </GameDescription>

                <Box display="flex" flexWrap="wrap" mb={2}>
                  {game.genre.map((g) => (
                    <GameGenre key={g} label={g} size="small" />
                  ))}
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center">
                    <StarIcon sx={{ color: '#ffc107', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      {game.rating}
                    </Typography>
                  </Box>
                  <Typography variant="h6" sx={{ color: game.isFree ? '#4caf50' : '#ff5722' }}>
                    {game.isFree ? 'FREE' : `$${game.price}`}
                  </Typography>
                </Box>
              </CardContent>
            </GameCard>
          </Grid>
        ))}
      </Grid>

      {filteredGames.length === 0 && (
        <Box textAlign="center" py={8}>
          <Typography variant="h5" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
            No games found
          </Typography>
          <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
            Try adjusting your search or filters
          </Typography>
        </Box>
      )}
    </StyledContainer>
  );
};

export default GamesPage;