import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Tabs,
  Tab,
  Container,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  PlayArrow as PlayIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Download as DownloadIcon,
  Star as StarIcon
} from '@mui/icons-material';
import styled from 'styled-components';
import { GameLoadingScreen } from '../components/common/GameLoadingScreen';
import apiService, { Game } from '../services/api';

const StyledContainer = styled(Container)`
  padding: 2rem 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
`;

const GameCard = styled(Card)`
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.3);
    border-color: rgba(255, 87, 34, 0.5);
  }
`;

const GameImage = styled(CardMedia)`
  height: 200px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  }
`;

const PlayButton = styled(Button)`
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: rgba(255, 87, 34, 0.9) !important;
  color: white !important;
  border-radius: 50% !important;
  width: 60px !important;
  height: 60px !important;
  min-width: 60px !important;
  opacity: 0;
  transition: all 0.3s ease !important;
  z-index: 2;

  ${GameCard}:hover & {
    opacity: 1;
  }

  &:hover {
    background: rgba(255, 87, 34, 1) !important;
    transform: translate(-50%, -50%) scale(1.1) !important;
  }
`;

const GameTitle = styled(Typography)`
  color: white !important;
  font-weight: bold !important;
  margin-bottom: 0.5rem !important;
`;

const GameDescription = styled(Typography)`
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 0.9rem !important;
  margin-bottom: 1rem !important;
`;

const GameGenre = styled(Chip)`
  background: rgba(255, 87, 34, 0.2) !important;
  color: #ff5722 !important;
  border: 1px solid rgba(255, 87, 34, 0.3) !important;
  margin-right: 0.5rem !important;
  margin-bottom: 0.5rem !important;
`;

const SearchField = styled(TextField)`
  .MuiOutlinedInput-root {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 25px;

    fieldset {
      border-color: rgba(255, 255, 255, 0.2);
    }

    &:hover fieldset {
      border-color: rgba(255, 87, 34, 0.5);
    }

    &.Mui-focused fieldset {
      border-color: #ff5722;
    }

    input {
      color: white;
    }
  }
`;

const StyledTabs = styled(Tabs)`
  .MuiTabs-indicator {
    background-color: #ff5722;
  }

  .MuiTab-root {
    color: rgba(255, 255, 255, 0.7);

    &.Mui-selected {
      color: #ff5722;
    }
  }
`;

// Game interface is now imported from api service

export const GamesPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [games, setGames] = useState<Game[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingGames, setIsLoadingGames] = useState(false);

  useEffect(() => {
    loadGames();
  }, [selectedTab, searchTerm]);

  const loadGames = async () => {
    try {
      setIsLoadingGames(true);
      setError(null);

      // Determine status filter based on selected tab
      let status: 'installed' | 'favorites' | 'free' | undefined;
      switch (selectedTab) {
        case 1:
          status = 'installed';
          break;
        case 2:
          status = 'favorites';
          break;
        case 3:
          status = 'free';
          break;
        default:
          status = undefined;
      }

      const response = await apiService.getGames({
        search: searchTerm || undefined,
        status,
        limit: 20,
        page: 1
      });

      if (response.success && response.data) {
        setGames(response.data);
      } else {
        setError(response.error || 'Failed to load games');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load games');
      console.error('Failed to load games:', err);
    } finally {
      setIsLoadingGames(false);
    }
  };

  const handleLoadingComplete = () => {
    setLoading(false);
  };

  const handlePlayGame = async (game: Game) => {
    console.log('Playing game:', game.title);
    // Add game launch logic here - could integrate with Electron main process
    // window.electronAPI?.launchGame(game.id);
  };

  const handleDownloadGame = async (game: Game) => {
    try {
      console.log('Installing game:', game.title);
      const response = await apiService.installGame(game.id);
      if (response.success && response.data) {
        // Update the game in the local state
        setGames(prev => prev.map(g =>
          g.id === game.id ? response.data! : g
        ));
      }
    } catch (err) {
      console.error('Failed to install game:', err);
      setError(err instanceof Error ? err.message : 'Failed to install game');
    }
  };

  const toggleFavorite = async (gameId: string) => {
    try {
      const response = await apiService.toggleFavorite(gameId);
      if (response.success && response.data) {
        // Update the game in the local state
        setGames(prev => prev.map(game =>
          game.id === gameId ? response.data! : game
        ));
      }
    } catch (err) {
      console.error('Failed to toggle favorite:', err);
      setError(err instanceof Error ? err.message : 'Failed to update favorite');
    }
  };

  // Games are already filtered by the API, so we just use them directly
  const filteredGames = games;

  if (loading) {
    return <GameLoadingScreen onComplete={handleLoadingComplete} />;
  }

  return (
    <StyledContainer maxWidth="xl">
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box mb={4}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ color: 'white', fontWeight: 'bold' }}>
          Game Library
        </Typography>
        <Typography variant="h6" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 3 }}>
          Discover and play amazing games
        </Typography>

        <Box display="flex" gap={2} mb={3} flexWrap="wrap">
          <SearchField
            placeholder="Search games..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: 'rgba(255, 255, 255, 0.5)' }} />
                </InputAdornment>
              ),
            }}
            sx={{ flexGrow: 1, minWidth: 300 }}
          />
        </Box>

        <StyledTabs
          value={selectedTab}
          onChange={(_, newValue) => setSelectedTab(newValue)}
          sx={{ mb: 3 }}
        >
          <Tab label="All Games" />
          <Tab label="Installed" />
          <Tab label="Favorites" />
          <Tab label="Free" />
        </StyledTabs>
      </Box>

      {isLoadingGames && (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress sx={{ color: '#ff5722' }} />
        </Box>
      )}

      <Grid container spacing={3}>
        {filteredGames.map((game) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={game.id}>
            <GameCard>
              <Box position="relative">
                <GameImage
                  image={game.image}
                  title={game.title}
                />
                <PlayButton
                  onClick={() => game.isInstalled ? handlePlayGame(game) : handleDownloadGame(game)}
                >
                  {game.isInstalled ? <PlayIcon /> : <DownloadIcon />}
                </PlayButton>
              </Box>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                  <GameTitle variant="h6">
                    {game.title}
                  </GameTitle>
                  <IconButton
                    size="small"
                    onClick={() => toggleFavorite(game.id)}
                    sx={{ color: game.isFavorite ? '#ff5722' : 'rgba(255, 255, 255, 0.5)' }}
                  >
                    {game.isFavorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                  </IconButton>
                </Box>

                <GameDescription variant="body2">
                  {game.description}
                </GameDescription>

                <Box display="flex" flexWrap="wrap" mb={2}>
                  {game.genre.map((g) => (
                    <GameGenre key={g} label={g} size="small" />
                  ))}
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center">
                    <StarIcon sx={{ color: '#ffc107', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      {game.rating}
                    </Typography>
                  </Box>
                  <Typography variant="h6" sx={{ color: game.isFree ? '#4caf50' : '#ff5722' }}>
                    {game.isFree ? 'FREE' : `$${game.price}`}
                  </Typography>
                </Box>
              </CardContent>
            </GameCard>
          </Grid>
        ))}
      </Grid>

      {filteredGames.length === 0 && (
        <Box textAlign="center" py={8}>
          <Typography variant="h5" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
            No games found
          </Typography>
          <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
            Try adjusting your search or filters
          </Typography>
        </Box>
      )}
    </StyledContainer>
  );
};

export default GamesPage;