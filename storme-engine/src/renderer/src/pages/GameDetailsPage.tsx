import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

interface GameDetails {
  id: string;
  title: string;
  developer: string;
  publisher: string;
  releaseDate: string;
  description: string;
  coverImage?: string;
  screenshots?: string[];
  playTime?: number;
  lastPlayed?: string;
  genres?: string[];
}

const GameDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [game, setGame] = useState<GameDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  useEffect(() => {
    // In a real app, this would fetch the game details from an API
    const fetchGameDetails = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock data - replace with actual API call
        const mockGame: GameDetails = {
          id: id || '1',
          title: 'Example Game',
          developer: 'Example Studio',
          publisher: 'Example Publisher',
          releaseDate: '2023-01-01',
          description: 'This is an example game description. It would contain details about the game, its features, and other relevant information.',
          coverImage: 'https://via.placeholder.com/400x600',
          screenshots: [
            'https://via.placeholder.com/1920x1080',
            'https://via.placeholder.com/1920x1080',
            'https://via.placeholder.com/1920x1080',
          ],
          playTime: 12.5,
          lastPlayed: '2023-10-15T14:30:00Z',
          genres: ['Action', 'Adventure', 'RPG']
        };
        
        setGame(mockGame);
      } catch (error) {
        console.error('Error fetching game details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGameDetails();
  }, [id]);

  const handlePlay = () => {
    setIsPlaying(true);
    // In a real app, this would launch the game
    console.log('Launching game:', game?.title);
  };

  const handleBack = () => {
    navigate(-1);
  };

  if (isLoading) {
    return <div className="loading">Loading game details...</div>;
  }

  if (!game) {
    return <div className="error">Game not found</div>;
  }

  return (
    <div className="game-details">
      <div className="game-header" style={{ backgroundImage: `url(${game.coverImage})` }}>
            <div className="game-header-overlay"></div>
            <div className="game-header-content">
              <button onClick={handleBack} className="btn btn-back">
                ← Back to Library
              </button>
              <div className="game-title-container">
                <h1>{game.title}</h1>
                <div className="game-meta">
                  <span>{game.developer}</span>
                  <span>•</span>
                  <span>Released: {new Date(game.releaseDate).toLocaleDateString()}</span>
                  {game.playTime && <span>•</span>}
                  {game.playTime && <span>{game.playTime} hours played</span>}
                </div>
              </div>
              <div className="game-actions">
                <button onClick={handlePlay} className="btn btn-play" disabled={isPlaying}>
                  {isPlaying ? 'Playing...' : 'Play'}
                </button>
              </div>
            </div>
          </div>

          <div className="game-content">
            <div className="game-tabs">
              <button 
                className={`tab ${activeTab === 'details' ? 'active' : ''}`}
                onClick={() => setActiveTab('details')}
              >
                Details
              </button>
              <button 
                className={`tab ${activeTab === 'screenshots' ? 'active' : ''}`}
                onClick={() => setActiveTab('screenshots')}
              >
                Screenshots
              </button>
              <button 
                className={`tab ${activeTab === 'achievements' ? 'active' : ''}`}
                onClick={() => setActiveTab('achievements')}
              >
                Achievements
              </button>
            </div>

            <div className="tab-content">
              {activeTab === 'details' && (
                <div className="game-description">
                  <h3>About</h3>
                  <p>{game.description}</p>
                  
                  <div className="game-info-grid">
                    <div className="info-item">
                      <span className="info-label">Developer</span>
                      <span className="info-value">{game.developer}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Publisher</span>
                      <span className="info-value">{game.publisher}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Release Date</span>
                      <span className="info-value">
                        {new Date(game.releaseDate).toLocaleDateString()}
                      </span>
                    </div>
                    {game.genres && game.genres.length > 0 && (
                      <div className="info-item">
                        <span className="info-label">Genres</span>
                        <span className="info-value">
                          {game.genres.join(', ')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {activeTab === 'screenshots' && (
                <div className="screenshots-grid">
                  {game.screenshots?.map((screenshot, index) => (
                    <div key={index} className="screenshot-item">
                      <img src={screenshot} alt={`Screenshot ${index + 1}`} />
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'achievements' && (
                <div className="achievements-container">
                  <p>No achievements available for this game.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    };
    
    export default GameDetailsPage;
