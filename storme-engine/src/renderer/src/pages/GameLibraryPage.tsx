import React, { useState } from 'react';
import { Link } from 'react-router-dom';

interface Game {
  id: string;
  title: string;
  developer: string;
  coverImage?: string;
  lastPlayed?: string;
  playTime?: number;
}

const GameLibraryPage: React.FC = () => {
  const [games, setGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter games based on search query
  const filteredGames = games.filter(game =>
    game.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    game.developer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="game-library">
      <div className="library-header">
        <h1>Game Library</h1>
        <input
          type="text"
          placeholder="Search games..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="search-input"
        />
      </div>

      {isLoading ? (
        <div className="loading">Loading your games...</div>
      ) : filteredGames.length > 0 ? (
        <div className="game-grid">
          {filteredGames.map((game) => (
            <Link to={`/game/${game.id}`} key={game.id} className="game-card">
              {game.coverImage ? (
                <img src={game.coverImage} alt={game.title} className="game-cover" />
              ) : (
                <div className="game-cover-placeholder">
                  {game.title.charAt(0).toUpperCase()}
                </div>
              )}
              <div className="game-info">
                <h3>{game.title}</h3>
                <p>{game.developer}</p>
                {game.lastPlayed && (
                  <small>Last played: {game.lastPlayed}</small>
                )}
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="empty-library">
          <p>No games found in your library.</p>
          <button className="btn btn-primary">Add Games</button>
        </div>
      )}
    </div>
  );
};

export default GameLibraryPage;
