import React, { useState } from 'react';

interface Settings {
  theme: 'light' | 'dark' | 'system';
  language: string;
  enableAnalytics: boolean;
  notifications: {
    gameUpdates: boolean;
    friendOnline: boolean;
    achievements: boolean;
  };
  gameLibraryPath: string;
  maxConcurrentDownloads: number;
}

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<Settings>({
    theme: 'dark',
    language: 'en-US',
    enableAnalytics: true,
    notifications: {
      gameUpdates: true,
      friendOnline: true,
      achievements: true,
    },
    gameLibraryPath: 'C:\\Games',
    maxConcurrentDownloads: 3,
  });

  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [showRestartPrompt, setShowRestartPrompt] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;
      setSettings(prev => ({
        ...prev,
        [name]: target.checked
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [name]: checked
      }
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    // Simulate API call to save settings
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSaving(false);
    setShowRestartPrompt(true);
  };

  const handleRestart = () => {
    // In a real app, this would restart the application
    console.log('Restarting application...');
    setShowRestartPrompt(false);
  };

  const handleChooseFolder = () => {
    // In a real app, this would open a folder dialog
    console.log('Opening folder dialog...');
  };

  return (
    <div className="settings-page">
      <h1>Settings</h1>
      
      <div className="settings-container">
        <div className="settings-sidebar">
          <button 
            className={`sidebar-item ${activeTab === 'general' ? 'active' : ''}`}
            onClick={() => setActiveTab('general')}
          >
            General
          </button>
          <button 
            className={`sidebar-item ${activeTab === 'appearance' ? 'active' : ''}`}
            onClick={() => setActiveTab('appearance')}
          >
            Appearance
          </button>
          <button 
            className={`sidebar-item ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => setActiveTab('notifications')}
          >
            Notifications
          </button>
          <button 
            className={`sidebar-item ${activeTab === 'downloads' ? 'active' : ''}`}
            onClick={() => setActiveTab('downloads')}
          >
            Downloads
          </button>
          <button 
            className={`sidebar-item ${activeTab === 'about' ? 'active' : ''}`}
            onClick={() => setActiveTab('about')}
          >
            About
          </button>
        </div>

        <div className="settings-content">
          {activeTab === 'general' && (
            <div className="settings-section">
              <h2>General Settings</h2>
              <div className="form-group">
                <label htmlFor="language">Language</label>
                <select
                  id="language"
                  name="language"
                  value={settings.language}
                  onChange={handleChange}
                >
                  <option value="en-US">English (US)</option>
                  <option value="es-ES">Español</option>
                  <option value="fr-FR">Français</option>
                  <option value="de-DE">Deutsch</option>
                  <option value="ja-JP">日本語</option>
                  <option value="zh-CN">中文 (简体)</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    name="enableAnalytics"
                    checked={settings.enableAnalytics}
                    onChange={handleChange}
                  />
                  <span>Enable analytics</span>
                </label>
                <p className="hint">Help improve Storme Engine by sending anonymous usage data</p>
              </div>
            </div>
          )}

          {activeTab === 'appearance' && (
            <div className="settings-section">
              <h2>Appearance</h2>
              <div className="form-group">
                <label htmlFor="theme">Theme</label>
                <select
                  id="theme"
                  name="theme"
                  value={settings.theme}
                  onChange={handleChange}
                >
                  <option value="dark">Dark</option>
                  <option value="light">Light</option>
                  <option value="system">System Default</option>
                </select>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="settings-section">
              <h2>Notification Settings</h2>
              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    name="gameUpdates"
                    checked={settings.notifications.gameUpdates}
                    onChange={handleNotificationChange}
                  />
                  <span>Game updates</span>
                </label>
              </div>
              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    name="friendOnline"
                    checked={settings.notifications.friendOnline}
                    onChange={handleNotificationChange}
                  />
                  <span>Friend online</span>
                </label>
              </div>
              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    name="achievements"
                    checked={settings.notifications.achievements}
                    onChange={handleNotificationChange}
                  />
                  <span>Achievements</span>
                </label>
              </div>
            </div>
          )}

          {activeTab === 'downloads' && (
            <div className="settings-section">
              <h2>Download Settings</h2>
              <div className="form-group">
                <label htmlFor="gameLibraryPath">Game Library Location</label>
                <div className="path-selector">
                  <input
                    type="text"
                    id="gameLibraryPath"
                    name="gameLibraryPath"
                    value={settings.gameLibraryPath}
                    onChange={handleChange}
                    readOnly
                  />
                  <button onClick={handleChooseFolder} className="btn btn-secondary">
                    Browse...
                  </button>
                </div>
                <p className="hint">Location where your games will be installed</p>
              </div>
              
              <div className="form-group">
                <label htmlFor="maxConcurrentDownloads">Maximum Concurrent Downloads</label>
                <input
                  type="range"
                  id="maxConcurrentDownloads"
                  name="maxConcurrentDownloads"
                  min="1"
                  max="10"
                  value={settings.maxConcurrentDownloads}
                  onChange={handleChange}
                />
                <div className="range-value">{settings.maxConcurrentDownloads}</div>
              </div>
            </div>
          )}

          {activeTab === 'about' && (
            <div className="settings-section">
              <h2>About Storme Engine</h2>
              <div className="about-info">
                <p>Version 1.0.0</p>
                <p>© 2025 Storme Team. All rights reserved.</p>
                <div className="about-links">
                  <a href="#privacy">Privacy Policy</a>
                  <span>•</span>
                  <a href="#terms">Terms of Service</a>
                  <span>•</span>
                  <a href="#licenses">Third-Party Licenses</a>
                </div>
              </div>
            </div>
          )}

          <div className="settings-actions">
            <button 
              onClick={handleSave} 
              className="btn btn-primary"
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>

      {showRestartPrompt && (
        <div className="restart-prompt">
          <div className="restart-content">
            <h3>Restart Required</h3>
            <p>Some changes will take effect after you restart the application.</p>
            <div className="restart-actions">
              <button 
                onClick={() => setShowRestartPrompt(false)}
                className="btn btn-secondary"
              >
                Later
              </button>
              <button 
                onClick={handleRestart}
                className="btn btn-primary"
              >
                Restart Now
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsPage;
