import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  Container,
  Tabs,
  Tab,
  IconButton,
  Paper
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Games as GamesIcon,
  Code as CodeIcon,
  Analytics as AnalyticsIcon,
  MonetizationOn as MonetizationIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import styled from 'styled-components';
import { useAppContext } from '../contexts/AppContext';

const StyledContainer = styled(Container)`
  padding: 2rem 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
`;

const DashboardCard = styled(Card)`
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 87, 34, 0.2);
    border-color: rgba(255, 87, 34, 0.3);
  }
`;

const StatCard = styled(DashboardCard)`
  text-align: center;
  padding: 1.5rem;
`;

const WelcomeCard = styled(DashboardCard)`
  background: linear-gradient(135deg, rgba(255, 87, 34, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%) !important;
  border: 1px solid rgba(255, 87, 34, 0.3) !important;
`;

const QuickActionButton = styled(Button)`
  background: rgba(255, 87, 34, 0.1) !important;
  color: #ff5722 !important;
  border: 1px solid rgba(255, 87, 34, 0.3) !important;
  margin: 0.5rem !important;

  &:hover {
    background: rgba(255, 87, 34, 0.2) !important;
    border-color: rgba(255, 87, 34, 0.5) !important;
  }
`;

const RecentGameCard = styled(Card)`
  background: rgba(255, 255, 255, 0.03) !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.08) !important;
    border-color: rgba(255, 87, 34, 0.3);
  }
`;

const StyledTabs = styled(Tabs)`
  .MuiTabs-indicator {
    background-color: #ff5722;
  }

  .MuiTab-root {
    color: rgba(255, 255, 255, 0.7);

    &.Mui-selected {
      color: #ff5722;
    }
  }
`;

interface UserStats {
  gamesPlayed: number;
  totalPlaytime: string;
  achievements: number;
  level: number;
  experience: number;
  maxExperience: number;
}

interface DeveloperStats {
  totalGames: number;
  totalDownloads: number;
  revenue: number;
  activeUsers: number;
}

interface RecentGame {
  id: string;
  title: string;
  lastPlayed: string;
  playtime: string;
  image: string;
}

const mockUserStats: UserStats = {
  gamesPlayed: 24,
  totalPlaytime: '156h 32m',
  achievements: 89,
  level: 12,
  experience: 2450,
  maxExperience: 3000
};

const mockDeveloperStats: DeveloperStats = {
  totalGames: 5,
  totalDownloads: 12500,
  revenue: 8750.50,
  activeUsers: 3200
};

const mockRecentGames: RecentGame[] = [
  {
    id: '1',
    title: 'Cyber Legends',
    lastPlayed: '2 hours ago',
    playtime: '24h 15m',
    image: '/api/placeholder/60/60'
  },
  {
    id: '2',
    title: 'Space Explorer',
    lastPlayed: 'Yesterday',
    playtime: '18h 42m',
    image: '/api/placeholder/60/60'
  },
  {
    id: '3',
    title: 'Medieval Kingdoms',
    lastPlayed: '3 days ago',
    playtime: '45h 20m',
    image: '/api/placeholder/60/60'
  }
];

export const DashboardPage: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [userType, setUserType] = useState<'gamer' | 'developer'>('gamer'); // This should come from context
  const { user } = useAppContext();

  // Mock user data - in real app this would come from context/API
  const userData = {
    name: user?.displayName || 'GameStorme User',
    avatar: user?.photoURL || '/api/placeholder/80/80',
    userType: userType
  };

  const renderGamerDashboard = () => (
    <>
      {/* Welcome Section */}
      <Grid item xs={12}>
        <WelcomeCard>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar
                src={userData.avatar}
                sx={{ width: 80, height: 80 }}
              />
              <Box flex={1}>
                <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                  Welcome back, {userData.name}!
                </Typography>
                <Typography variant="h6" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                  Level {mockUserStats.level} Gamer
                </Typography>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    Experience: {mockUserStats.experience}/{mockUserStats.maxExperience}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(mockUserStats.experience / mockUserStats.maxExperience) * 100}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#ff5722'
                    }
                  }}
                />
              </Box>
            </Box>
          </CardContent>
        </WelcomeCard>
      </Grid>

      {/* Stats Cards */}
      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <GamesIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            {mockUserStats.gamesPlayed}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Games Played
          </Typography>
        </StatCard>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <PlayIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            {mockUserStats.totalPlaytime}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Total Playtime
          </Typography>
        </StatCard>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <TrendingUpIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            {mockUserStats.achievements}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Achievements
          </Typography>
        </StatCard>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <PeopleIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            {mockUserStats.level}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Current Level
          </Typography>
        </StatCard>
      </Grid>

      {/* Quick Actions */}
      <Grid item xs={12} md={6}>
        <DashboardCard>
          <CardContent>
            <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
              Quick Actions
            </Typography>
            <Box display="flex" flexWrap="wrap">
              <QuickActionButton startIcon={<GamesIcon />}>
                Browse Games
              </QuickActionButton>
              <QuickActionButton startIcon={<PeopleIcon />}>
                Find Friends
              </QuickActionButton>
              <QuickActionButton startIcon={<TrendingUpIcon />}>
                View Achievements
              </QuickActionButton>
              <QuickActionButton startIcon={<SettingsIcon />}>
                Settings
              </QuickActionButton>
            </Box>
          </CardContent>
        </DashboardCard>
      </Grid>

      {/* Recent Games */}
      <Grid item xs={12} md={6}>
        <DashboardCard>
          <CardContent>
            <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
              Recently Played
            </Typography>
            {mockRecentGames.map((game) => (
              <RecentGameCard key={game.id}>
                <CardContent sx={{ py: 1.5 }}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar src={game.image} variant="rounded" />
                    <Box flex={1}>
                      <Typography variant="subtitle1" sx={{ color: 'white' }}>
                        {game.title}
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                        {game.lastPlayed} • {game.playtime}
                      </Typography>
                    </Box>
                    <IconButton sx={{ color: '#ff5722' }}>
                      <PlayIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </RecentGameCard>
            ))}
          </CardContent>
        </DashboardCard>
      </Grid>
    </>
  );

  const renderDeveloperDashboard = () => (
    <>
      {/* Welcome Section */}
      <Grid item xs={12}>
        <WelcomeCard>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar
                src={userData.avatar}
                sx={{ width: 80, height: 80 }}
              />
              <Box flex={1}>
                <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                  Welcome back, {userData.name}!
                </Typography>
                <Typography variant="h6" sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                  Game Developer
                </Typography>
                <Chip
                  label="Pro Developer"
                  sx={{
                    background: 'linear-gradient(135deg, #ff5722 0%, #ff9800 100%)',
                    color: 'white',
                    fontWeight: 'bold'
                  }}
                />
              </Box>
            </Box>
          </CardContent>
        </WelcomeCard>
      </Grid>

      {/* Developer Stats */}
      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <GamesIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            {mockDeveloperStats.totalGames}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Published Games
          </Typography>
        </StatCard>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <TrendingUpIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            {mockDeveloperStats.totalDownloads.toLocaleString()}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Total Downloads
          </Typography>
        </StatCard>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <MonetizationIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            ${mockDeveloperStats.revenue.toLocaleString()}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Total Revenue
          </Typography>
        </StatCard>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <StatCard>
          <PeopleIcon sx={{ fontSize: 40, color: '#ff5722', mb: 1 }} />
          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
            {mockDeveloperStats.activeUsers.toLocaleString()}
          </Typography>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Active Users
          </Typography>
        </StatCard>
      </Grid>

      {/* Developer Quick Actions */}
      <Grid item xs={12} md={6}>
        <DashboardCard>
          <CardContent>
            <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
              Developer Tools
            </Typography>
            <Box display="flex" flexWrap="wrap">
              <QuickActionButton startIcon={<CodeIcon />}>
                New Project
              </QuickActionButton>
              <QuickActionButton startIcon={<AnalyticsIcon />}>
                Analytics
              </QuickActionButton>
              <QuickActionButton startIcon={<MonetizationIcon />}>
                Monetization
              </QuickActionButton>
              <QuickActionButton startIcon={<NotificationsIcon />}>
                AI Marketing
              </QuickActionButton>
            </Box>
          </CardContent>
        </DashboardCard>
      </Grid>

      {/* Recent Projects */}
      <Grid item xs={12} md={6}>
        <DashboardCard>
          <CardContent>
            <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
              Recent Projects
            </Typography>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)', textAlign: 'center', py: 4 }}>
              Your recent projects will appear here
            </Typography>
          </CardContent>
        </DashboardCard>
      </Grid>
    </>
  );

  return (
    <StyledContainer maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ color: 'white', fontWeight: 'bold' }}>
          Dashboard
        </Typography>

        <StyledTabs
          value={selectedTab}
          onChange={(_, newValue) => {
            setSelectedTab(newValue);
            setUserType(newValue === 0 ? 'gamer' : 'developer');
          }}
          sx={{ mb: 3 }}
        >
          <Tab label="Gamer Dashboard" />
          <Tab label="Developer Dashboard" />
        </StyledTabs>
      </Box>

      <Grid container spacing={3}>
        {selectedTab === 0 ? renderGamerDashboard() : renderDeveloperDashboard()}
      </Grid>
    </StyledContainer>
  );
};

export default DashboardPage;