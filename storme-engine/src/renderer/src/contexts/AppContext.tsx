import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ipc<PERSON><PERSON>er } from 'electron';
import { IPC_CHANNELS } from '../../../common/ipc-channels';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  preferences?: {
    theme?: string;
    language?: string;
    notifications?: boolean;
  };
}

interface AppState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  settings: {
    darkMode: boolean;
    sidebarCollapsed: boolean;
    notifications: boolean;
    analytics: boolean;
  };
}

interface AppContextType extends AppState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: { name: string; email: string; password: string }) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  toggleSidebar: () => void;
  toggleDarkMode: () => void;
  setError: (error: string | null) => void;
}

const initialState: AppState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
  settings: {
    darkMode: true,
    sidebarCollapsed: false,
    notifications: true,
    analytics: true,
  },
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AppState>(initialState);
  const navigate = useNavigate();

  // Load initial state from localStorage or settings
  useEffect(() => {
    const loadInitialState = async () => {
      try {
        // Check if user is already logged in
        const token = localStorage.getItem('authToken');
        if (token) {
          // Validate token with main process
          const user = await ipcRenderer.invoke(IPC_CHANNELS.AUTH_VALIDATE_TOKEN, token);
          if (user) {
            setState(prev => ({
              ...prev,
              isAuthenticated: true,
              user,
            }));
          }
        }

        // Load settings
        const settings = await ipcRenderer.invoke(IPC_CHANNELS.SETTINGS_GET);
        if (settings) {
          setState(prev => ({
            ...prev,
            settings: {
              ...prev.settings,
              ...settings,
            },
          }));
        }
      } catch (error) {
        console.error('Failed to load initial state:', error);
      }
    };

    loadInitialState();

    // Listen for logout event from main process
    const handleLogoutEvent = () => {
      logout();
    };

    ipcRenderer.on(IPC_CHANNELS.AUTH_LOGOUT, handleLogoutEvent);

    return () => {
      ipcRenderer.off(IPC_CHANNELS.AUTH_LOGOUT, handleLogoutEvent);
    };
  }, []);

  // Apply theme when dark mode changes
  useEffect(() => {
    if (state.settings.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [state.settings.darkMode]);

  const login = async (email: string, password: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const { user, token } = await ipcRenderer.invoke(IPC_CHANNELS.AUTH_LOGIN, { email, password });
      
      // Save token to localStorage
      localStorage.setItem('authToken', token);
      
      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user,
        loading: false,
      }));
      
      // Navigate to dashboard
      navigate('/dashboard');
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to login',
      }));
      throw error;
    }
  };

  const register = async (userData: { name: string; email: string; password: string }) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      await ipcRenderer.invoke(IPC_CHANNELS.AUTH_REGISTER, userData);
      setState(prev => ({ ...prev, loading: false }));
      
      // Auto-login after registration
      await login(userData.email, userData.password);
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to register',
      }));
      throw error;
    }
  };

  const logout = () => {
    // Clear token from localStorage
    localStorage.removeItem('authToken');
    
    // Reset state
    setState(initialState);
    
    // Navigate to login
    navigate('/login');
    
    // Notify main process
    ipcRenderer.send(IPC_CHANNELS.AUTH_LOGOUT);
  };

  const updateUser = (userData: Partial<User>) => {
    if (!state.user) return;
    
    setState(prev => ({
      ...prev,
      user: {
        ...prev.user!,
        ...userData,
      },
    }));
    
    // Update user in main process
    ipcRenderer.send(IPC_CHANNELS.USER_UPDATE, userData);
  };

  const toggleSidebar = () => {
    const newCollapsedState = !state.settings.sidebarCollapsed;
    setState(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        sidebarCollapsed: newCollapsedState,
      },
    }));
    
    // Save setting
    ipcRenderer.send(IPC_CHANNELS.SETTINGS_UPDATE, {
      sidebarCollapsed: newCollapsedState,
    });
  };

  const toggleDarkMode = () => {
    const newDarkModeState = !state.settings.darkMode;
    setState(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        darkMode: newDarkModeState,
      },
    }));
    
    // Save setting
    ipcRenderer.send(IPC_CHANNELS.SETTINGS_UPDATE, {
      darkMode: newDarkModeState,
    });
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  return (
    <AppContext.Provider
      value={{
        ...state,
        login,
        register,
        logout,
        updateUser,
        toggleSidebar,
        toggleDarkMode,
        setError,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
