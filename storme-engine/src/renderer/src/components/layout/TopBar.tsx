import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { theme } from '../../../../theme';
import { FiSearch, FiBell, FiMessageSquare, FiMenu, FiX, FiUser, FiLogOut, FiSettings } from 'react-icons/fi';
import { Avatar } from '../common/Avatar';
import { IconButton } from '../common/Button';
import { Badge } from '../common/Badge';
import { useAppContext } from '../../contexts/AppContext';

const TopBarContainer = styled.header`
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  height: 64px;
  background: rgba(13, 12, 34, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: ${theme.zIndex.overlay - 1};
  display: flex;
  align-items: center;
  padding: 0 ${theme.spacing.lg};
  transition: all ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
`;

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  max-width: 600px;
  margin: 0 ${theme.spacing.lg};
`;

const SearchInput = styled.input`
  padding: ${theme.spacing.sm} ${theme.spacing.md} ${theme.spacing.sm} ${theme.spacing.xxl};
  border-radius: ${theme.shape.borderRadius}px;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.05);
  color: ${theme.colors.textPrimary};
  font-size: ${theme.typography.body2.fontSize};
  transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  width: 100%;

  &::placeholder {
    color: ${theme.colors.textTertiary};
  }

  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 2px rgba(123, 101, 237, 0.2);
    background: rgba(123, 101, 237, 0.1);
  }
`;

const SearchIcon = styled(FiSearch)`
  position: absolute;
  left: ${theme.spacing.md};
  top: 50%;
  transform: translateY(-50%);
  color: ${theme.colors.textTertiary};
`;

const Actions = styled.div`
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: ${theme.spacing.sm};
`;

const NotificationMenu = styled.div`
  position: relative;
`;

const DropdownMenu = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: calc(100% + ${theme.spacing.sm});
  right: 0;
  width: 320px;
  background: ${theme.colors.surface};
  border-radius: ${theme.shape.borderRadius * 2}px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  visibility: ${({ isOpen }) => (isOpen ? 'visible' : 'hidden')};
  transform: ${({ isOpen }) => (isOpen ? 'translateY(0)' : 'translateY(10px)')};
  transition: all ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
  z-index: ${theme.zIndex.tooltip + 1};
`;

const DropdownHeader = styled.div`
  padding: ${theme.spacing.md};
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const DropdownTitle = styled.h3`
  margin: 0;
  font-size: ${theme.typography.body2.fontSize};
  font-weight: ${theme.typography.fontWeightBold};
  color: ${theme.colors.textSecondary};
`;

const DropdownContent = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const NotificationItem = styled.div`
  padding: ${theme.spacing.md};
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: background ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const NotificationTitle = styled.div`
  font-weight: ${theme.typography.fontWeightBold};
  margin-bottom: ${theme.spacing.xs};
`;

const NotificationTime = styled.div`
  font-size: ${theme.typography.caption.fontSize};
  color: ${theme.colors.textTertiary};
  transition: all ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
`;

const UserMenu = styled.div`
  position: relative;
  margin-left: ${theme.spacing.sm};
`;

const UserButton = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  border-radius: ${theme.shape.borderRadius / 2}px;
  padding: ${theme.spacing.xs} ${theme.spacing.md};
  margin: ${theme.spacing.xs} ${theme.spacing.sm};
  transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  font-weight: ${theme.typography.fontWeightBold};
  color: ${theme.colors.textPrimary};
  cursor: pointer;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const UserName = styled.span`
  margin: 0 ${theme.spacing.sm};
  font-weight: ${theme.typography.fontWeightBold};
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const UserDropdown = styled(DropdownMenu)`
  width: 200px;
  right: 0;
`;

const UserDropdownItem = styled.div`
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  
  svg {
    margin-right: ${theme.spacing.sm};
    color: ${theme.colors.textTertiary};
    transition: all ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
  }
`;

interface TopBarProps {
  onToggleSidebar: () => void;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    role: string;
  } | null;
}

export const TopBar: React.FC<TopBarProps> = ({ onToggleSidebar, user }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { logout } = useAppContext();

  // Mock notifications data
  const [notifications, setNotifications] = useState([
    { id: '1', title: 'New message', message: 'You have a new message from John', time: '2m ago', read: false },
    { id: '2', title: 'Game update', message: 'A new update is available', time: '1h ago', read: false },
    { id: '3', title: 'Welcome', message: 'Welcome to Storme Engine!', time: '1d ago', read: true },
  ]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  return (
    <TopBarContainer>
      <IconButton onClick={onToggleSidebar} variant="ghost">
        <FiMenu />
      </IconButton>

      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="Search..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <SearchIcon />
      </SearchContainer>

      <Actions>
        <NotificationMenu ref={notificationsRef}>
          <IconButton
            onClick={() => setNotificationsOpen(!notificationsOpen)}
            variant={notificationsOpen ? 'primary' : 'ghost'}
          >
            <Badge count={notifications.filter(n => !n.read).length}>
              <FiBell />
            </Badge>
          </IconButton>
          
          <DropdownMenu isOpen={notificationsOpen}>
            <DropdownHeader>
              <DropdownTitle>Notifications</DropdownTitle>
              <IconButton variant="ghost">
                <FiSettings />
              </IconButton>
            </DropdownHeader>
            <DropdownContent>
              {notifications.length > 0 ? (
                notifications.map((notification) => (
                  <NotificationItem key={notification.id}>
                    <NotificationTitle>{notification.title}</NotificationTitle>
                    <div>{notification.message}</div>
                    <NotificationTime>{notification.time}</NotificationTime>
                  </NotificationItem>
                ))
              ) : (
                <div style={{ padding: theme.spacing.md, textAlign: 'center', color: theme.colors.textSecondary }}>
                  No new notifications
                </div>
              )}
            </DropdownContent>
          </DropdownMenu>
        </NotificationMenu>

        <UserMenu ref={userMenuRef}>
          <UserButton onClick={() => setUserMenuOpen(!userMenuOpen)}>
            <Avatar size="sm" src={user?.avatar} name={user?.name} />
            <UserName>{user?.name}</UserName>
          </UserButton>
          
          <UserDropdown isOpen={userMenuOpen}>
            <UserDropdownItem>
              <FiUser />
              <span>Profile</span>
            </UserDropdownItem>
            <UserDropdownItem>
              <FiSettings />
              <span>Settings</span>
            </UserDropdownItem>
            <UserDropdownItem onClick={handleLogout}>
              <FiLogOut />
              <span>Logout</span>
            </UserDropdownItem>
          </UserDropdown>
        </UserMenu>
      </Actions>
    </TopBarContainer>
  );
};

export default TopBar;
