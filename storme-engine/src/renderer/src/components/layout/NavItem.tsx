import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { theme } from '../../../../theme';

const NavItemContainer = styled.li`
  position: relative;
  margin: ${theme.spacing.xs} ${theme.spacing.sm};
  border-radius: ${theme.shape.borderRadius}px;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${theme.colors.primary};
    transform: scaleY(0);
    transform-origin: top;
    transition: transform ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
  }
  
  &:hover::before {
    transform: scaleY(1);
  }
  
  &.active {
    &::before {
      transform: scaleY(1);
    }
    
    a {
      background: rgba(123, 101, 237, 0.1);
      color: ${theme.colors.primary};
      
      svg {
        color: ${theme.colors.primary};
      }
    }
  }
`;

const NavLink = styled(Link)<{ collapsed: boolean }>`
  display: flex;
  align-items: center;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  color: ${theme.colors.textSecondary};
  text-decoration: none;
  border-radius: ${theme.shape.borderRadius}px;
  transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  white-space: nowrap;
  
  &:hover {
    color: ${theme.colors.textPrimary};
    background: rgba(255, 255, 255, 0.05);
    
    svg {
      color: ${theme.colors.primary};
    }
  }
  
  svg {
    flex-shrink: 0;
    font-size: 1.25rem;
    margin-right: ${({ collapsed }) => (collapsed ? 0 : theme.spacing.sm)};
    transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
    color: ${theme.colors.textTertiary};
  }
  
  span {
    opacity: ${({ collapsed }) => (collapsed ? 0 : 1)};
    transition: opacity ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  ${({ collapsed }) => collapsed && `
    justify-content: center;
    padding: ${theme.spacing.sm};
    
    &::after {
      content: attr(data-tooltip);
      position: absolute;
      left: 100%;
      top: 50%;
      transform: translateY(-50%);
      background: ${theme.colors.surface};
      color: ${theme.colors.textPrimary};
      padding: ${theme.spacing.xs} ${theme.spacing.sm};
      border-radius: ${theme.shape.borderRadius / 2}px;
      font-size: ${theme.typography.caption.fontSize};
      white-space: nowrap;
      pointer-events: none;
      opacity: 0;
      visibility: hidden;
      transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      z-index: ${theme.zIndex.tooltip};
    }
    
    &:hover::after {
      opacity: 1;
      visibility: visible;
      left: calc(100% + ${theme.spacing.sm});
    }
  `}
`;

interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  to: string;
  collapsed: boolean;
  active?: boolean;
  badge?: number;
  exact?: boolean;
}

export const NavItem: React.FC<NavItemProps> = ({
  icon,
  label,
  to,
  collapsed,
  active = false,
  badge,
  exact = false,
}) => {
  const location = useLocation();
  const isActive = exact
    ? location.pathname === to
    : location.pathname.startsWith(to);

  return (
    <NavItemContainer className={isActive ? 'active' : ''}>
      <NavLink 
        to={to} 
        collapsed={collapsed}
        data-tooltip={collapsed ? label : undefined}
      >
        {icon}
        <span>{label}</span>
        {badge !== undefined && badge > 0 && !collapsed && (
          <Badge count={badge} style={{ marginLeft: 'auto' }} />
        )}
      </NavLink>
    </NavItemContainer>
  );
};

// Simple Badge component for notification counts
const Badge = styled.span<{ count: number }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 ${({ count }) => (count > 9 ? '4px' : '6px')};
  border-radius: 10px;
  background: ${({ theme }) => theme.colors.error};
  color: white;
  font-size: 0.6875rem;
  font-weight: ${({ theme }) => theme.typography.fontWeightBold};
  margin-left: auto;
`;
