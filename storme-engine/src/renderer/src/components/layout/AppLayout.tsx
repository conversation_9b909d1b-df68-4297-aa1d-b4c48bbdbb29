import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import theme from '../../theme';
import { Sidebar } from './Sidebar';
import { TopBar } from './TopBar';
import { useAppContext } from '../../contexts/AppContext';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: ${theme.palette.background.default};
  color: ${theme.palette.text.primary};
  font-family: ${theme.typography.fontFamily};
`;

const MainContent = styled.main<{ sidebarCollapsed: boolean }>`
  flex: 1;
  margin-left: ${({ sidebarCollapsed }) => (sidebarCollapsed ? '80px' : '280px')};
  transition: margin ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
  overflow-x: hidden;
  padding-top: 64px;
  min-height: 100vh;
  background: ${theme.colors.background};
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(66, 41, 188, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(240, 188, 43, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(123, 101, 237, 0.1) 0%, transparent 50%);
  background-attachment: fixed;
`;

const ContentWrapper = styled.div`
  padding: ${theme.spacing(4)};
  ${theme.breakpoints.up('lg')} {
    padding: ${theme.spacing(5)};
  }
  max-width: ${theme.breakpoints.values.xl}px;
  margin: 0 auto;
  width: 100%;
  min-height: calc(100vh - 64px);
`;

interface AppLayoutProps {
  children: React.ReactNode;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { isAuthenticated, user } = useAppContext();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect to login if not authenticated and not on auth pages
  useEffect(() => {
    const publicPaths = ['/login', '/register', '/forgot-password'];
    if (!isAuthenticated && !publicPaths.includes(location.pathname)) {
      navigate('/login', { state: { from: location } });
    }
  }, [isAuthenticated, location, navigate]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Don't show layout for auth pages
  if (!isAuthenticated && ['/login', '/register', '/forgot-password'].includes(location.pathname)) {
    return <>{children}</>;
  }

  return (
    <LayoutContainer>
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={toggleSidebar} />
      <MainContent sidebarCollapsed={sidebarCollapsed}>
        <TopBar onToggleSidebar={toggleSidebar} user={user} />
        <ContentWrapper>{children}</ContentWrapper>
      </MainContent>
    </LayoutContainer>
  );
};
