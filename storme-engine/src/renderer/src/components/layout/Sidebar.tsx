import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { theme } from '../../../../theme';
import { Logo } from '../common/Logo';
import { NavItem } from './NavItem';
import { FiHome, FiCompass, FiUsers, FiSettings, FiBox, FiAward, FiDollarSign, FiBarChart2 } from 'react-icons/fi';
import { FaGamepad, FaRobot } from 'react-icons/fa';
import { BsLightningChargeFill } from 'react-icons/bs';

const SidebarContainer = styled.div<{ collapsed: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: ${({ collapsed }) => (collapsed ? '80px' : '280px')};
  height: 100vh;
  background: rgba(13, 12, 34, 0.8);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  z-index: ${theme.zIndex.drawer};
  transition: all ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const LogoContainer = styled.div<{ collapsed: boolean }>`
  display: flex;
  align-items: center;
  justify-content: ${({ collapsed }) => (collapsed ? 'center' : 'flex-start')};
  padding: ${({ collapsed }) => (collapsed ? theme.spacing.md : `${theme.spacing.lg} ${theme.spacing.md}`)};
  height: 64px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const Nav = styled.nav`
  flex: 1;
  overflow-y: auto;
  padding: ${theme.spacing.md} 0;
  scrollbar-width: thin;
  scrollbar-color: ${theme.colors.primary} transparent;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: ${theme.colors.primary};
    border-radius: 20px;
  }
`;

const NavSection = styled.div`
  margin-bottom: ${theme.spacing.lg};
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const NavSectionTitle = styled.div`
  padding: 0 ${theme.spacing.md} ${theme.spacing.xs};
  font-size: ${theme.typography.caption.fontSize};
  color: ${theme.colors.textTertiary};
  text-transform: uppercase;
  letter-spacing: ${theme.typography.letterSpacingWide};
  font-weight: ${theme.typography.fontWeightBold};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const NavItems = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const Footer = styled.footer<{ collapsed: boolean }>`
  padding: ${theme.spacing.md};
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: ${({ collapsed }) => (collapsed ? 'center' : 'left')};
`;

const CollapseButton = styled.button`
  background: none;
  border: none;
  color: ${theme.colors.textSecondary};
  cursor: pointer;
  padding: ${theme.spacing.sm};
  border-radius: ${theme.shape.borderRadius}px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: ${theme.colors.textPrimary};
  }
  
  svg {
    font-size: 1.25rem;
  }
`;

interface SidebarProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ collapsed, onToggleCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  return (
    <SidebarContainer collapsed={collapsed}>
      <LogoContainer collapsed={collapsed}>
        <Logo collapsed={collapsed} />
      </LogoContainer>
      
      <Nav>
        <NavSection>
          <NavItems>
            <NavItem
              icon={<FiHome />}
              label="Dashboard"
              to="/dashboard"
              collapsed={collapsed}
              active={isActive('/dashboard')}
            />
            <NavItem
              icon={<FaGamepad />}
              label="My Games"
              to="/games"
              collapsed={collapsed}
              active={isActive('/games')}
            />
            <NavItem
              icon={<FiCompass />}
              label="Discover"
              to="/discover"
              collapsed={collapsed}
              active={isActive('/discover')}
            />
            <NavItem
              icon={<FiUsers />}
              label="Community"
              to="/community"
              collapsed={collapsed}
              active={isActive('/community')}
            />
          </NavItems>
        </NavSection>
        
        <NavSection>
          <NavSectionTitle style={{ display: collapsed ? 'none' : 'block' }}>
            Developer
          </NavSectionTitle>
          <NavItems>
            <NavItem
              icon={<FiBox />}
              label="My Projects"
              to="/developer/projects"
              collapsed={collapsed}
              active={isActive('/developer/projects')}
            />
            <NavItem
              icon={<FiBarChart2 />}
              label="Analytics"
              to="/developer/analytics"
              collapsed={collapsed}
              active={isActive('/developer/analytics')}
            />
            <NavItem
              icon={<FaRobot />}
              label="AI Marketing"
              to="/developer/ai-marketing"
              collapsed={collapsed}
              active={isActive('/developer/ai-marketing')}
            />
            <NavItem
              icon={<FiDollarSign />}
              label="Monetization"
              to="/developer/monetization"
              collapsed={collapsed}
              active={isActive('/developer/monetization')}
            />
          </NavItems>
        </NavSection>
        
        <NavSection>
          <NavSectionTitle style={{ display: collapsed ? 'none' : 'block' }}>
            Tools
          </NavSectionTitle>
          <NavItems>
            <NavItem
              icon={<BsLightningChargeFill />}
              label="Game Booster"
              to="/tools/booster"
              collapsed={collapsed}
              active={isActive('/tools/booster')}
            />
            <NavItem
              icon={<FiSettings />}
              label="Settings"
              to="/settings"
              collapsed={collapsed}
              active={isActive('/settings')}
            />
          </NavItems>
        </NavSection>
      </Nav>
      
      <Footer collapsed={collapsed}>
        <CollapseButton onClick={onToggleCollapse} title={collapsed ? 'Expand' : 'Collapse'}>
          {collapsed ? '→' : '←'}
        </CollapseButton>
      </Footer>
    </SidebarContainer>
  );
};
