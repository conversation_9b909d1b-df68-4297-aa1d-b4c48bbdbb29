import React from 'react';
import styled from 'styled-components';
import theme from '../../theme/theme';

interface AvatarProps {
  src?: string;
  name?: string;
  size?: number;
  className?: string;
  onClick?: () => void;
}

const AvatarContainer = styled.div<{ size: number }>`
  width: ${({ size }) => size}px;
  height: ${({ size }) => size}px;
  border-radius: 50%;
  background: ${theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: ${({ onClick }) => (onClick ? 'pointer' : 'default')};
  transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  flex-shrink: 0;
  
  &:hover {
    transform: ${({ onClick }) => (onClick ? 'scale(1.05)' : 'none')};
    box-shadow: ${({ onClick }) =>
      onClick ? `0 0 0 2px ${theme.colors.primaryLight}` : 'none'};
  }
`;

const AvatarImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const AvatarText = styled.span<{ size: number }>`
  color: white;
  font-weight: ${theme.typography.fontWeightBold};
  font-size: ${({ size }) => Math.max(size * 0.4, 12)}px;
  text-transform: uppercase;
  user-select: none;
`;

export const Avatar: React.FC<AvatarProps> = ({
  src,
  name,
  size = 40,
  className,
  onClick,
}) => {
  const getInitials = (fullName?: string) => {
    if (!fullName) return 'U';
    
    const names = fullName.trim().split(' ');
    if (names.length === 1) return names[0].charAt(0).toUpperCase();
    
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  return (
    <AvatarContainer 
      size={size} 
      className={className}
      onClick={onClick}
      title={name}
    >
      {src ? (
        <AvatarImage 
          src={src} 
          alt={name ? `${name}'s avatar` : 'User avatar'} 
          onError={(e) => {
            // Fallback to initials if image fails to load
            if (name) {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const textElement = document.createElement('span');
              textElement.textContent = getInitials(name);
              textElement.style.color = 'white';
              textElement.style.fontWeight = 'bold';
              textElement.style.fontSize = `${Math.max(size * 0.4, 12)}px`;
              target.parentNode?.appendChild(textElement);
            }
          }}
        />
      ) : (
        <AvatarText size={size}>
          {getInitials(name)}
        </AvatarText>
      )}
    </AvatarContainer>
  );
};

export default Avatar;
