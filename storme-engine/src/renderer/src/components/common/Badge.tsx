import React from 'react';
import styled, { css } from 'styled-components';
import theme from '../../theme/theme';

type BadgeVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
type BadgeSize = 'sm' | 'md' | 'lg';

interface BadgeProps {
  /** The content of the badge */
  children?: React.ReactNode;
  /** The variant of the badge */
  variant?: BadgeVariant;
  /** The size of the badge */
  size?: BadgeSize;
  /** If provided, will show a dot indicator instead of text */
  dot?: boolean;
  /** The number to show in the badge (overrides children if both are provided) */
  count?: number;
  /** The maximum number to show in the badge (for count) */
  maxCount?: number;
  /** Whether to show a border around the badge */
  bordered?: boolean;
  /** Custom class name */
  className?: string;
  /** Custom styles */
  style?: React.CSSProperties;
}

const badgeSizes = {
  sm: {
    fontSize: theme.typography.caption.fontSize,
    padding: '2px 6px',
    dotSize: 6,
  },
  md: {
    fontSize: theme.typography.caption.fontSize,
    padding: '4px 8px',
    dotSize: 8,
  },
  lg: {
    fontSize: theme.typography.body2.fontSize,
    padding: '6px 10px',
    dotSize: 10,
  },
};

const BadgeContainer = styled.span<{
  $variant: BadgeVariant;
  $size: BadgeSize;
  $dot: boolean;
  $bordered: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px; /* Using full pixel value instead of theme.borderRadius.pill */
  font-weight: ${theme.typography.fontWeightMedium};
  line-height: 1;
  white-space: nowrap;
  vertical-align: middle;
  box-sizing: border-box;
  text-align: center;
  min-width: ${({ $dot, $size }) => 
    $dot ? 'auto' : $size === 'sm' ? '18px' : $size === 'lg' ? '26px' : '22px'};
  height: ${({ $dot, $size }) => 
    $dot ? badgeSizes[$size].dotSize + 'px' : 'auto'};
  padding: ${({ $dot, $size }) => $dot ? '0' : badgeSizes[$size].padding};
  font-size: ${({ $size }) => badgeSizes[$size].fontSize};
  
  /* Variant styles */
  ${({ $variant, $bordered }) => {
    switch ($variant) {
      case 'primary':
        return css`
          background: ${theme.palette.primary.main};
          color: ${theme.palette.primary.contrastText};
          ${$bordered && `border: 1px solid ${theme.palette.primary.dark};`}
        `;
      
      case 'secondary':
        return css`
          background: ${theme.palette.secondary.main};
          color: ${theme.palette.secondary.contrastText};
          ${$bordered && `border: 1px solid ${theme.palette.secondary.dark};`}
        `;
      
      case 'success':
        return css`
          background: ${theme.palette.success.main};
          color: ${theme.palette.success.contrastText};
          ${$bordered && `border: 1px solid ${theme.palette.success.dark};`}
        `;
      
      case 'warning':
        return css`
          background: ${theme.palette.warning.main};
          color: ${theme.palette.warning.contrastText};
          ${$bordered && `border: 1px solid ${theme.palette.warning.dark};`}
        `;
      
      case 'error':
        return css`
          background: ${theme.palette.error.main};
          color: ${theme.palette.error.contrastText};
          ${$bordered && `border: 1px solid ${theme.palette.error.dark};`}
        `;
      
      case 'info':
        return css`
          background: ${theme.palette.info.main};
          color: ${theme.palette.info.contrastText};
          ${$bordered && `border: 1px solid ${theme.palette.info.dark};`}
        `;
      
      default: // default
        return css`
          background: ${theme.palette.grey[200]};
          color: ${theme.palette.text.primary};
          ${$bordered && `border: 1px solid ${theme.palette.divider};`}
        `;
    }
  }}
  
  /* Dot variant */
  ${({ $dot, $size }) =>
    $dot &&
    css`
      padding: 0;
      width: ${badgeSizes[$size].dotSize}px;
      height: ${badgeSizes[$size].dotSize}px;
      border-radius: 50%;
      min-width: auto;
    `}
`;

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  dot = false,
  count,
  maxCount = 99,
  bordered = false,
  className,
  style,
}) => {
  // If count is provided, it takes precedence over children
  const content = React.useMemo(() => {
    if (dot) return null;
    
    if (count !== undefined) {
      return count > maxCount ? `${maxCount}+` : count.toString();
    }
    
    return children;
  }, [children, count, dot, maxCount]);

  return (
    <BadgeContainer
      $variant={variant}
      $size={size}
      $dot={dot}
      $bordered={bordered}
      className={className}
      style={style}
      aria-label={typeof content === 'string' ? content : undefined}
    >
      {!dot && content}
    </BadgeContainer>
  );
};

export default Badge;
