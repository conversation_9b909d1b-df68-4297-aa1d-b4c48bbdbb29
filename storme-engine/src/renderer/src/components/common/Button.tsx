import React, { ButtonHTMLAttributes, forwardRef } from 'react';
import styled, { css } from 'styled-components';
import theme from '../../theme/theme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'link';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children?: React.ReactNode;
  as?: React.ElementType;
  to?: string;
}

const buttonSizes = {
  sm: {
    padding: '4px 8px',
    fontSize: theme.typography.caption.fontSize,
    iconSize: '1rem',
  },
  md: {
    padding: '8px 16px',
    fontSize: theme.typography.body2.fontSize,
    iconSize: '1.25rem',
  },
  lg: {
    padding: '12px 24px',
    fontSize: theme.typography.body1.fontSize,
    iconSize: '1.5rem',
  },
};

const ButtonBase = styled.button<{
  $variant: ButtonVariant;
  $size: ButtonSize;
  $fullWidth: boolean;
  $isLoading: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${theme.spacing.xs};
  border-radius: ${theme.borderRadius.md};
  font-weight: ${theme.typography.fontWeightMedium};
  font-family: ${theme.typography.fontFamily};
  cursor: pointer;
  transition: all ${theme.transitions.duration.short}ms ${theme.transitions.easing.easeInOut};
  ${({ $fullWidth }) =>
    $fullWidth &&
    css`
      width: 100%;
    `}
  text-decoration: none;
  width: ${({ $fullWidth }) => ($fullWidth ? '100%' : 'auto')};
  opacity: ${({ $isLoading }) => ($isLoading ? 0.7 : 1)};
  pointer-events: ${({ $isLoading }) => ($isLoading ? 'none' : 'auto')};
  position: relative;
  overflow: hidden;

  /* Size styles */
  ${({ $size }) => {
    const size = buttonSizes[$size];
    return css`
      padding: ${size.padding};
      font-size: ${size.fontSize};

      svg {
        width: ${size.iconSize};
        height: ${size.iconSize};
      }
    `;
  }}

  /* Variant styles */
  ${({ $variant }) => {
    switch ($variant) {
      case 'primary':
        return css`
          background: ${theme.palette.primary.main};
          color: ${theme.palette.primary.contrastText};

          &:hover:not(:disabled) {
            background: ${theme.palette.primary.dark};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            transform: translateY(0);
          }
        `;

      case 'secondary':
        return css`
          background: ${theme.palette.secondary.main};
          color: ${theme.palette.secondary.contrastText};

          &:hover:not(:disabled) {
            background: ${theme.palette.secondary.dark};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            transform: translateY(0);
          }
        `;

      case 'outline':
        return css`
          background: transparent;
          color: ${theme.palette.primary.main};
          border: 1px solid ${theme.palette.primary.main};

          &:hover:not(:disabled) {
            background: ${theme.palette.action.hover};
          }
        `;

      case 'ghost':
        return css`
          background: transparent;
          color: ${theme.palette.text.primary};

          &:hover:not(:disabled) {
            background: ${theme.palette.action.hover};
          }
        `;

      case 'danger':
        return css`
          background: ${theme.palette.error.main};
          color: ${theme.palette.error.contrastText};

          &:hover:not(:disabled) {
            background: ${theme.palette.error.dark};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            transform: translateY(0);
          }
        `;

      case 'link':
        return css`
          padding: 0;
          background: transparent;
          color: ${theme.palette.primary.main};
          text-decoration: underline;
          min-height: auto;
          height: auto;
          line-height: 1.5;

          &:hover:not(:disabled) {
            background: transparent;
            text-decoration: none;
            color: ${theme.palette.primary.dark};
          }
        `;

      default:
        return '';
    }
  }}

  
  /* Disabled state */
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Loading state */
  ${({ $isLoading }) =>
    $isLoading &&
    css`
      &::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin: -8px 0 0 -8px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top-color: white;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
      }
      
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    `}
`;

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      isLoading = false,
      leftIcon,
      rightIcon,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    return (
      <ButtonBase
        ref={ref}
        $variant={variant}
        $size={size}
        $fullWidth={fullWidth}
        $isLoading={isLoading}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading ? (
          <span style={{ visibility: 'hidden' }}>
            {leftIcon}
            {children}
            {rightIcon}
          </span>
        ) : (
          <>
            {leftIcon && <span>{leftIcon}</span>}
            {children && <span>{children}</span>}
            {rightIcon && <span>{rightIcon}</span>}
          </>
        )}
      </ButtonBase>
    );
  }
);

Button.displayName = 'Button';

export const IconButton = styled(Button).attrs({
  variant: 'ghost',
  size: 'md',
})`
  padding: ${theme.spacing.xs};
  border-radius: 50%;
  width: ${({ size }) => (size === 'sm' ? '32px' : size === 'lg' ? '48px' : '40px')};
  height: ${({ size }) => (size === 'sm' ? '32px' : size === 'lg' ? '48px' : '40px')};
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: ${({ theme, variant }) =>
      variant === 'ghost' ? theme.colors.backgroundLight : 'inherit'};
  }
  
  svg {
    width: ${({ size }) => (size === 'sm' ? '16px' : size === 'lg' ? '24px' : '20px')};
    height: ${({ size }) => (size === 'sm' ? '16px' : size === 'lg' ? '24px' : '20px')};
  }
`;

export const ButtonGroup = styled.div`
  display: inline-flex;
  border-radius: ${theme.borderRadius.md};
  overflow: hidden;
  
  ${ButtonBase} {
    border-radius: 0;
    margin: 0;
    position: relative;
    
    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    &:first-child {
      border-top-left-radius: ${theme.borderRadius.md};
      border-bottom-left-radius: ${theme.borderRadius.md};
    }
    
    &:last-child {
      border-top-right-radius: ${theme.borderRadius.md};
      border-bottom-right-radius: ${theme.borderRadius.md};
    }
  }
`;
