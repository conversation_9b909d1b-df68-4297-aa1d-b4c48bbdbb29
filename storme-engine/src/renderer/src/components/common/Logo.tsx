import React from 'react';
import styled from 'styled-components';
import theme from '../../theme/theme';

const LogoContainer = styled.div<{ collapsed: boolean }>`
  display: flex;
  align-items: center;
  justify-content: ${({ collapsed }) => (collapsed ? 'center' : 'flex-start')};
  padding: ${theme.spacing(2)} ${theme.spacing(2)} ${theme.spacing(3)};
  margin-bottom: ${theme.spacing(2)};
  overflow: hidden;
  transition: all ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
`;

const LogoIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  margin-right: ${theme.spacing(1)};
  background: ${theme.colors.primary};
  color: white;
  border-radius: 8px;
  font-size: 1.25rem;
  font-weight: ${theme.typography.fontWeightBold};
  flex-shrink: 0;
`;

const LogoText = styled.div<{ collapsed: boolean }>`
  font-size: 1.25rem;
  font-weight: ${theme.typography.fontWeightBold};
  color: ${theme.colors.textPrimary};
  transition: all ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut};
  margin-left: ${({ collapsed }) => (collapsed ? 0 : theme.spacing(1))};
  opacity: ${({ collapsed }) => (collapsed ? 0 : 1)};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

interface LogoProps {
  collapsed?: boolean;
}

export const Logo: React.FC<LogoProps> = ({ collapsed = false }) => {
  return (
    <LogoContainer collapsed={collapsed}>
      <LogoIcon>G</LogoIcon>
      {!collapsed && <LogoText>GameStorm</LogoText>}
    </LogoContainer>
  );
};

export default Logo;
