import React, { ReactNode } from 'react';
import styled, { css } from 'styled-components';
import { theme } from '../../../theme';

type CardVariant = 'elevated' | 'outlined' | 'filled';
type CardSize = 'sm' | 'md' | 'lg';

interface CardProps {
  /** The content of the card */
  children: ReactNode;
  /** The variant of the card */
  variant?: CardVariant;
  /** The size of the card */
  size?: CardSize;
  /** Whether the card is hoverable */
  hoverable?: boolean;
  /** Whether the card is clickable */
  clickable?: boolean;
  /** Custom class name */
  className?: string;
  /** Custom styles */
  style?: React.CSSProperties;
  /** Click handler */
  onClick?: () => void;
}

const cardSizes = {
  sm: {
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  md: {
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
  },
  lg: {
    padding: theme.spacing.xl,
    borderRadius: theme.borderRadius.xl,
  },
};

const CardContainer = styled.div<{
  $variant: CardVariant;
  $size: CardSize;
  $hoverable: boolean;
  $clickable: boolean;
}>`
  position: relative;
  background: ${theme.colors.surface};
  transition: all ${theme.transitions.normal};
  overflow: hidden;
  width: 100%; // Ensure card takes full width of its container
  
  /* Size styles */
  ${({ $size }) => {
    const size = cardSizes[$size];
    return css`
      padding: ${size.padding};
      border-radius: ${size.borderRadius};
    `;
  }}
  
  /* Variant styles */
  ${({ $variant }) => {
    switch ($variant) {
      case 'elevated':
        return css`
          box-shadow: ${theme.shadows.sm};
          border: 1px solid ${theme.colors.border};
        `;
      
      case 'outlined':
        return css`
          border: 1px solid ${theme.colors.border};
          background: transparent;
        `;
      
      case 'filled':
        return css`
          background: ${theme.colors.backgroundLight};
          border: 1px solid ${theme.colors.border};
        `;
      
      default:
        return '';
    }
  }}
  
  /* Hover and click effects */
  ${({ $hoverable, $clickable, $variant }) => {
    if ($hoverable || $clickable) {
      return css`
        cursor: ${$clickable ? 'pointer' : 'default'};
        transition: all ${theme.transitions.normal};
        
        &:hover {
          transform: ${$hoverable ? 'translateY(-2px)' : 'none'};
          box-shadow: ${$variant === 'elevated' 
            ? theme.shadows.md 
            : $variant === 'outlined' || $variant === 'filled'
              ? `0 2px 8px rgba(0, 0, 0, 0.1)`
              : theme.shadows.sm};
          
          ${$variant === 'outlined' && `border-color: ${theme.colors.primaryLight};`}
        }
        
        &:active {
          transform: ${$clickable ? 'translateY(0)' : 'translateY(-2px)'};
          transition: all ${theme.transitions.fast};
        }
      `;
    }
    return '';
  }}
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing.md};
  
  h2, h3, h4 {
    margin: 0;
    color: ${theme.colors.textPrimary};
  }
`;

const CardTitle = styled.h3`
  font-size: ${theme.typography.h5.fontSize};
  font-weight: ${theme.typography.fontWeightBold};
  margin: 0 0 ${theme.spacing.sm} 0;
  color: ${theme.colors.textPrimary};
`;

const CardSubtitle = styled.p`
  font-size: ${theme.typography.body2.fontSize};
  color: ${theme.colors.textSecondary};
  margin: 0 0 ${theme.spacing.md} 0;
`;

const CardContent = styled.div`
  margin: ${theme.spacing.md} 0;
  
  &:first-child {
    margin-top: 0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: ${theme.spacing.md};
  padding-top: ${theme.spacing.md};
  border-top: 1px solid ${theme.colors.border};
  gap: ${theme.spacing.sm};
`;

const CardActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${theme.spacing.sm};
  margin-top: ${theme.spacing.md};
`;

interface CardComponent extends React.FC<CardProps> {
  Title: typeof CardTitle;
  Subtitle: typeof CardSubtitle;
  Content: typeof CardContent;
  Header: typeof CardHeader;
  Footer: typeof CardFooter;
  Actions: typeof CardActions;
}

export const Card: CardComponent = ({
  children,
  variant = 'elevated',
  size = 'md',
  hoverable = false,
  clickable = false,
  className,
  style,
  onClick,
}) => {
  return (
    <CardContainer
      $variant={variant}
      $size={size}
      $hoverable={hoverable}
      $clickable={clickable}
      className={className}
      style={style}
      onClick={onClick}
      role={clickable ? 'button' : 'article'}
      tabIndex={clickable ? 0 : -1}
      onKeyDown={(e) => {
        if (clickable && (e.key === 'Enter' || e.key === ' ') && onClick) {
          e.preventDefault();
          onClick();
        }
      }}
    >
      {children}
    </CardContainer>
  );
};

// Attach sub-components
Card.Title = CardTitle;
Card.Subtitle = CardSubtitle;
Card.Content = CardContent;
Card.Header = CardHeader;
Card.Footer = CardFooter;
Card.Actions = CardActions;

export default Card;
