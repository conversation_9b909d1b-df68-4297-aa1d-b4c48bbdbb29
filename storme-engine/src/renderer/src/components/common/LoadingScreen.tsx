import React from 'react';
import styled, { keyframes } from 'styled-components';
import { theme } from '../../../../theme';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: ${theme.colors.background};
  color: ${theme.colors.textPrimary};
`;

const Spinner = styled.div`
  width: 50px;
  height: 50px;
  border: 5px solid ${theme.colors.divider};
  border-top: 5px solid ${theme.colors.primary};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: ${theme.spacing.md};
`;

const LoadingText = styled.p`
  font-size: ${theme.typography.h5};
  color: ${theme.colors.textSecondary};
  margin-top: ${theme.spacing.md};
`;

interface LoadingScreenProps {
  text?: string;
  fullScreen?: boolean;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  text = 'Loading...',
  fullScreen = true,
}) => {
  return (
    <LoadingContainer style={!fullScreen ? { height: '100%', minHeight: '200px' } : {}}>
      <Spinner />
      {text && <LoadingText>{text}</LoadingText>}
    </LoadingContainer>
  );
};

export default LoadingScreen;
