import React from 'react';
import styled, { keyframes } from 'styled-components';
import { theme } from '../../../../theme';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
  color: ${theme.colors.textPrimary};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 87, 34, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
`;

const Logo = styled.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff5722 0%, #ff9800 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  margin-right: 1rem;
  animation: ${pulse} 2s ease-in-out infinite;
  box-shadow: 0 10px 30px rgba(255, 87, 34, 0.3);
`;

const BrandText = styled.div`
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(135deg, #ff5722 0%, #ff9800 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Spinner = styled.div`
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 87, 34, 0.2);
  border-top: 4px solid #ff5722;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: 1.5rem;
`;

const LoadingText = styled.p`
  font-size: 1.2rem;
  color: ${theme.colors.textSecondary};
  margin-top: 1rem;
  text-align: center;
`;

const SubText = styled.p`
  font-size: 0.9rem;
  color: ${theme.colors.textSecondary};
  opacity: 0.7;
  margin-top: 0.5rem;
  text-align: center;
`;

interface LoadingScreenProps {
  text?: string;
  subText?: string;
  fullScreen?: boolean;
  showLogo?: boolean;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  text = 'Loading...',
  subText,
  fullScreen = true,
  showLogo = true,
}) => {
  return (
    <LoadingContainer style={!fullScreen ? { height: '100%', minHeight: '200px' } : {}}>
      {showLogo && (
        <LogoContainer>
          <Logo>GS</Logo>
          <BrandText>GameStorme</BrandText>
        </LogoContainer>
      )}
      <Spinner />
      {text && <LoadingText>{text}</LoadingText>}
      {subText && <SubText>{subText}</SubText>}
    </LoadingContainer>
  );
};

export default LoadingScreen;
