import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { theme } from '../../../../theme';

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

const progressAnimation = keyframes`
  0% { width: 0%; }
  100% { width: 100%; }
`;

const gameIconFloat = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(255, 87, 34, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 152, 0, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  animation: ${fadeIn} 0.8s ease-out;
`;

const GameIcon = styled.div`
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #ff5722 0%, #ff9800 100%);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  margin-bottom: 2rem;
  animation: ${gameIconFloat} 3s ease-in-out infinite;
  box-shadow: 
    0 20px 40px rgba(255, 87, 34, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ff5722 0%, #ff9800 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 3rem;
  text-align: center;
`;

const ProgressContainer = styled.div`
  width: 300px;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
`;

const ProgressBar = styled.div<{ progress: number }>`
  height: 100%;
  background: linear-gradient(90deg, #ff5722 0%, #ff9800 100%);
  border-radius: 3px;
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: ${progressAnimation} 2s ease-in-out infinite;
  }
`;

const ProgressText = styled.p`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin-bottom: 2rem;
`;

const LoadingSteps = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const StepText = styled.p<{ active?: boolean }>`
  font-size: 0.9rem;
  color: ${props => props.active ? '#ff5722' : 'rgba(255, 255, 255, 0.5)'};
  transition: color 0.3s ease;
  text-align: center;
`;

interface GameLoadingScreenProps {
  onComplete?: () => void;
}

const loadingSteps = [
  'Initializing game library...',
  'Loading game data...',
  'Connecting to servers...',
  'Preparing interface...',
  'Almost ready...'
];

export const GameLoadingScreen: React.FC<GameLoadingScreenProps> = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 15 + 5;
        
        // Update step based on progress
        const stepIndex = Math.floor((newProgress / 100) * loadingSteps.length);
        setCurrentStep(Math.min(stepIndex, loadingSteps.length - 1));
        
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            onComplete?.();
          }, 500);
          return 100;
        }
        
        return newProgress;
      });
    }, 200);

    return () => clearInterval(interval);
  }, [onComplete]);

  return (
    <LoadingContainer>
      <ContentWrapper>
        <GameIcon>🎮</GameIcon>
        <Title>Loading Games</Title>
        <Subtitle>Preparing your gaming experience</Subtitle>
        
        <ProgressContainer>
          <ProgressBar progress={progress} />
        </ProgressContainer>
        
        <ProgressText>{Math.round(progress)}%</ProgressText>
        
        <LoadingSteps>
          {loadingSteps.map((step, index) => (
            <StepText key={index} active={index === currentStep}>
              {step}
            </StepText>
          ))}
        </LoadingSteps>
      </ContentWrapper>
    </LoadingContainer>
  );
};

export default GameLoadingScreen;
