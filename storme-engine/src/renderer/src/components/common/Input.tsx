import React, { InputHTMLAttributes, forwardRef, useState } from 'react';
import styled, { css } from 'styled-components';
import { theme } from '../../../theme';

type InputVariant = 'default' | 'filled' | 'outlined';
type InputSize = 'sm' | 'md' | 'lg';

interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** The variant of the input */
  variant?: InputVariant;
  /** The size of the input */
  size?: InputSize;
  /** Whether the input is full width */
  fullWidth?: boolean;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Whether the input has an error */
  error?: boolean | string;
  /** The label for the input */
  label?: string;
  /** Helper text to display below the input */
  helperText?: string;
  /** Start adornment (e.g., icon) */
  startAdornment?: React.ReactNode;
  /** End adornment (e.g., icon) */
  endAdornment?: React.ReactNode;
  /** Custom class name */
  className?: string;
  /** Reference to the input element */
  ref?: React.Ref<HTMLInputElement>;
}

const inputSizes = {
  sm: {
    height: '32px',
    padding: `0 ${theme.spacing.sm}`,
    fontSize: theme.typography.caption,
    iconSize: '16px',
  },
  md: {
    height: '40px',
    padding: `0 ${theme.spacing.md}`,
    fontSize: theme.typography.body2,
    iconSize: '18px',
  },
  lg: {
    height: '48px',
    padding: `0 ${theme.spacing.lg}`,
    fontSize: theme.typography.body1,
    iconSize: '20px',
  },
};

const InputContainer = styled.div<{ $fullWidth: boolean }>`
  display: flex;
  flex-direction: column;
  width: ${({ $fullWidth }) => ($fullWidth ? '100%' : 'auto')};
  position: relative;
`;

const Label = styled.label<{ $error: boolean; $disabled: boolean }>`
  display: block;
  margin-bottom: ${theme.spacing.xs};
  font-size: ${theme.typography.caption};
  font-weight: ${theme.typography.fontWeightMedium};
  color: ${({ $error, $disabled }) =>
    $error ? theme.colors.error : $disabled ? theme.colors.textDisabled : theme.colors.textSecondary};
  transition: color ${theme.transitions.fast};
`;

const InputWrapper = styled.div<{
  $variant: InputVariant;
  $size: InputSize;
  $error: boolean;
  $disabled: boolean;
  $hasStartAdornment: boolean;
  $hasEndAdornment: boolean;
  $isFocused: boolean;
}>`
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: ${({ $size }) => inputSizes[$size].height};
  background: ${({ $variant }) =>
    $variant === 'filled' ? theme.colors.backgroundLight : 'transparent'};
  border: 1px solid
    ${({ $error, $isFocused, $variant }) =>
      $error
        ? theme.colors.error
        : $isFocused
        ? theme.colors.primary
        : $variant === 'outlined'
        ? theme.colors.border
        : 'transparent'};
  border-radius: ${theme.borderRadius.md};
  transition: all ${theme.transitions.fast};
  box-shadow: ${({ $isFocused, $variant }) =>
    $isFocused && $variant !== 'outlined' ? `0 0 0 3px ${theme.colors.primaryLight}40` : 'none'};

  &:hover {
    border-color: ${({ $error, $disabled, $variant }) =>
      $disabled
        ? 'transparent'
        : $error
        ? theme.colors.error
        : $variant === 'outlined'
        ? theme.colors.textSecondary
        : theme.colors.primaryLight};
  }
`;

const StyledInput = styled.input<{
  $size: InputSize;
  $hasStartAdornment: boolean;
  $hasEndAdornment: boolean;
  $disabled: boolean;
}>`
  flex: 1;
  width: 100%;
  height: 100%;
  padding: 0;
  padding-left: ${({ $hasStartAdornment, $size }) =>
    $hasStartAdornment ? theme.spacing.xs : inputSizes[$size].padding};
  padding-right: ${({ $hasEndAdornment, $size }) =>
    $hasEndAdornment ? theme.spacing.xs : inputSizes[$size].padding};
  border: none;
  background: transparent;
  color: ${({ $disabled }) =>
    $disabled ? theme.colors.textDisabled : theme.colors.textPrimary};
  font-size: ${({ $size }) => inputSizes[$size].fontSize};
  font-family: ${theme.typography.fontFamily};
  outline: none;
  transition: all ${theme.transitions.fast};
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  &::placeholder {
    color: ${theme.colors.textTertiary};
    opacity: 1;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

const Adornment = styled.span<{ $position: 'start' | 'end'; $size: InputSize }>`
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${theme.colors.textTertiary};
  padding: 0 ${({ $position, $size }) =>
    $position === 'start' ? inputSizes[$size].padding : theme.spacing.sm} 0
    ${({ $position, $size }) =>
      $position === 'end' ? inputSizes[$size].padding : theme.spacing.sm};
  
  svg {
    width: ${({ $size }) => inputSizes[$size].iconSize};
    height: ${({ $size }) => inputSizes[$size].iconSize};
  }
`;

const HelperText = styled.span<{ $error: boolean; $disabled: boolean }>`
  display: block;
  margin-top: ${theme.spacing.xs};
  font-size: ${theme.typography.caption};
  color: ${({ $error, $disabled }) =>
    $error ? theme.colors.error : $disabled ? theme.colors.textDisabled : theme.colors.textTertiary};
  min-height: 1em;
  line-height: 1.4;
`;

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      variant = 'default',
      size = 'md',
      fullWidth = false,
      disabled = false,
      error = false,
      label,
      helperText,
      startAdornment,
      endAdornment,
      className,
      onFocus,
      onBlur,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = useState(false);
    const hasError = !!error;
    const errorMessage = typeof error === 'string' ? error : '';

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    return (
      <InputContainer $fullWidth={fullWidth} className={className}>
        {label && (
          <Label $error={hasError} $disabled={disabled}>
            {label}
          </Label>
        )}
        
        <InputWrapper
          $variant={variant}
          $size={size}
          $error={hasError}
          $disabled={disabled}
          $hasStartAdornment={!!startAdornment}
          $hasEndAdornment={!!endAdornment}
          $isFocused={isFocused}
        >
          {startAdornment && (
            <Adornment $position="start" $size={size}>
              {startAdornment}
            </Adornment>
          )}
          
          <StyledInput
            ref={ref}
            $size={size}
            $hasStartAdornment={!!startAdornment}
            $hasEndAdornment={!!endAdornment}
            $disabled={disabled}
            disabled={disabled}
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...props}
          />
          
          {endAdornment && (
            <Adornment $position="end" $size={size}>
              {endAdornment}
            </Adornment>
          )}
        </InputWrapper>
        
        {(helperText || hasError) && (
          <HelperText $error={hasError} $disabled={disabled}>
            {hasError ? errorMessage : helperText}
          </HelperText>
        )}
      </InputContainer>
    );
  }
);

Input.displayName = 'Input';

export default Input;
