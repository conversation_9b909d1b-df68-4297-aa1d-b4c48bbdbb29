export enum GameInstallationStatus {
  NOT_INSTALLED = 'not_installed',
  INSTALLING = 'installing',
  INSTALLED = 'installed',
  UPDATING = 'updating',
  UNINSTALLING = 'uninstalling',
  ERROR = 'error',
}

export enum GamePlatform {
  WINDOWS = 'windows',
  MACOS = 'macos',
  LINUX = 'linux',
  ANDROID = 'android',
  IOS = 'ios',
  XBOX = 'xbox',
  PLAYSTATION = 'playstation',
  NINTENDO = 'nintendo',
  OTHER = 'other',
}

export interface GameScreenshot {
  id: string;
  url: string;
  width: number;
  height: number;
  caption?: string;
}

export interface GameAchievement {
  id: string;
  name: string;
  description: string;
  iconUrl: string;
  achieved: boolean;
  achievedAt?: string;
  globalAchievementRate?: number;
}

export interface GameDLC {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  isOwned: boolean;
  releaseDate?: string;
  bannerUrl?: string;
}

export interface GameUpdateInfo {
  available: boolean;
  currentVersion: string;
  newVersion?: string;
  releaseNotes?: string;
  size?: number; // in bytes
  isRequired?: boolean;
  releaseDate?: string;
}

export interface GameSaveData {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  fileSize: number;
  thumbnailUrl?: string;
  platform?: string;
}

export interface GameMod {
  id: string;
  name: string;
  author: string;
  version: string;
  description: string;
  isInstalled: boolean;
  installDate?: string;
  fileSize?: number;
  downloadUrl?: string;
  thumbnailUrl?: string;
  category?: string;
}

export interface Game {
  // Core properties
  id: string;
  title: string;
  description: string;
  developer: string;
  publisher: string;
  releaseDate: string;
  genres: string[];
  tags: string[];
  platforms: GamePlatform[];
  
  // Media
  coverImage?: string;
  bannerImage?: string;
  logoImage?: string;
  screenshots: GameScreenshot[];
  
  // Technical details
  version: string;
  executablePath?: string;
  installPath?: string;
  launchOptions?: string[];
  is64Bit?: boolean;
  requiresAdmin?: boolean;
  
  // Installation status
  status: GameInstallationStatus;
  installDate?: string;
  lastUpdated?: string;
  isFavorite: boolean;
  isHidden: boolean;
  
  // Play tracking
  isRunning: boolean;
  isUpdating: boolean;
  lastPlayed?: string;
  playCount: number;
  totalPlayTime: number; // in minutes
  
  // Additional metadata
  websiteUrl?: string;
  supportUrl?: string;
  forumUrl?: string;
  wikiUrl?: string;
  
  // User data
  notes?: string;
  rating?: number; // 0-5
  completionStatus?: 'not_started' | 'in_progress' | 'completed' | 'abandoned';
  
  // System
  dateAdded: string;
  lastModified: string;
  
  // Extensions
  achievements?: GameAchievement[];
  dlcs?: GameDLC[];
  saves?: GameSaveData[];
  mods?: GameMod[];
  
  // Cloud save support
  cloudSavesEnabled?: boolean;
  cloudSaveLastSync?: string;
  
  // Multiplayer info
  hasMultiplayer?: boolean;
  hasCoop?: boolean;
  maxPlayers?: number;
  
  // Content ratings
  esrbRating?: string;
  pegiRating?: string;
  contentDescriptors?: string[];
  
  // Technical requirements
  minimumRequirements?: {
    os?: string;
    processor?: string;
    memory?: string;
    graphics?: string;
    storage?: string;
    additionalNotes?: string;
  };
  
  recommendedRequirements?: {
    os?: string;
    processor?: string;
    memory?: string;
    graphics?: string;
    storage?: string;
    additionalNotes?: string;
  };
  
  // Custom properties
  [key: string]: any;
}

export interface GameLibraryStats {
  totalGames: number;
  installedGames: number;
  totalPlayTime: number; // in minutes
  totalAchievements: number;
  unlockedAchievements: number;
  mostPlayedGame?: string;
  recentlyPlayed: string[];
  byPlatform: Record<string, number>;
  byGenre: Record<string, number>;
}
