/**
 * IPC Channels for communication between main and renderer processes
 * This file defines all the IPC channel names used in the application
 * to ensure type safety and prevent typos.
 */

export const IPC_CHANNELS = {
  // App related channels
  APP_GET_VERSION: 'app:get-version',
  APP_GET_VERSION_REPLY: 'app:get-version-reply',
  
  APP_GET_NAME: 'app:get-name',
  APP_GET_NAME_REPLY: 'app:get-name-reply',
  
  APP_GET_PATH: 'app:get-path',
  APP_GET_PATH_REPLY: 'app:get-path-reply',
  
  APP_RELAUNCH: 'app:relaunch',
  APP_RELAUNCH_REPLY: 'app:relaunch-reply',
  
  APP_QUIT: 'app:quit',
  APP_QUIT_REPLY: 'app:quit-reply',
  
  // Window related channels
  WINDOW_MINIMIZE: 'window:minimize',
  WINDOW_MINIMIZE_REPLY: 'window:minimize-reply',
  
  WINDOW_MAXIMIZE: 'window:maximize',
  WINDOW_MAXIMIZE_REPLY: 'window:maximize-reply',
  
  WINDOW_CLOSE: 'window:close',
  WINDOW_CLOSE_REPLY: 'window:close-reply',
  
  WINDOW_TOGGLE_FULLSCREEN: 'window:toggle-fullscreen',
  WINDOW_TOGGLE_FULLSCREEN_REPLY: 'window:toggle-fullscreen-reply',
  
  WINDOW_TOGGLE_DEVTOOLS: 'window:toggle-dev-tools',
  WINDOW_TOGGLE_DEVTOOLS_REPLY: 'window:toggle-dev-tools-reply',
  
  // File System related channels
  FS_READ_FILE: 'fs:read-file',
  FS_READ_FILE_REPLY: 'fs:read-file-reply',
  
  FS_WRITE_FILE: 'fs:write-file',
  FS_WRITE_FILE_REPLY: 'fs:write-file-reply',
  
  FS_READ_DIR: 'fs:read-dir',
  FS_READ_DIR_REPLY: 'fs:read-dir-reply',
  
  FS_MKDIR: 'fs:mkdir',
  FS_MKDIR_REPLY: 'fs:mkdir-reply',
  
  FS_STAT: 'fs:stat',
  FS_STAT_REPLY: 'fs:stat-reply',
  
  FS_UNLINK: 'fs:unlink',
  FS_UNLINK_REPLY: 'fs:unlink-reply',
  
  FS_RENAME: 'fs:rename',
  FS_RENAME_REPLY: 'fs:rename-reply',
  
  // Dialog related channels
  DIALOG_SHOW_OPEN_DIALOG: 'dialog:show-open-dialog',
  DIALOG_SHOW_OPEN_DIALOG_REPLY: 'dialog:show-open-dialog-reply',
  
  DIALOG_SHOW_SAVE_DIALOG: 'dialog:show-save-dialog',
  DIALOG_SHOW_SAVE_DIALOG_REPLY: 'dialog:show-save-dialog-reply',
  
  DIALOG_SHOW_MESSAGE_BOX: 'dialog:show-message-box',
  DIALOG_SHOW_MESSAGE_BOX_REPLY: 'dialog:show-message-box-reply',
  
  DIALOG_SHOW_ERROR_BOX: 'dialog:show-error-box',
  DIALOG_SHOW_ERROR_BOX_REPLY: 'dialog:show-error-box-reply',
  
  // Shell related channels
  SHELL_OPEN_EXTERNAL: 'shell:open-external',
  SHELL_OPEN_EXTERNAL_REPLY: 'shell:open-external-reply',
  
  SHELL_OPEN_PATH: 'shell:open-path',
  SHELL_OPEN_PATH_REPLY: 'shell:open-path-reply',
  
  SHELL_SHOW_ITEM_IN_FOLDER: 'shell:show-item-in-folder',
  SHELL_SHOW_ITEM_IN_FOLDER_REPLY: 'shell:show-item-in-folder-reply',
  
  SHELL_BE_ITEM_IN_FOLDER: 'shell:be-item-in-folder',
  SHELL_BE_ITEM_IN_FOLDER_REPLY: 'shell:be-item-in-folder-reply',
  
  // Clipboard related channels
  CLIPBOARD_READ_TEXT: 'clipboard:read-text',
  CLIPBOARD_READ_TEXT_REPLY: 'clipboard:read-text-reply',
  
  CLIPBOARD_WRITE_TEXT: 'clipboard:write-text',
  CLIPBOARD_WRITE_TEXT_REPLY: 'clipboard:write-text-reply',
  
  CLIPBOARD_READ_HTML: 'clipboard:read-html',
  CLIPBOARD_READ_HTML_REPLY: 'clipboard:read-html-reply',
  
  CLIPBOARD_WRITE_HTML: 'clipboard:write-html',
  CLIPBOARD_WRITE_HTML_REPLY: 'clipboard:write-html-reply',
  
  CLIPBOARD_READ_RTF: 'clipboard:read-rtf',
  CLIPBOARD_READ_RTF_REPLY: 'clipboard:read-rtf-reply',
  
  CLIPBOARD_WRITE_RTF: 'clipboard:write-rtf',
  CLIPBOARD_WRITE_RTF_REPLY: 'clipboard:write-rtf-reply',
  
  CLIPBOARD_READ_BOOKMARK: 'clipboard:read-bookmark',
  CLIPBOARD_READ_BOOKMARK_REPLY: 'clipboard:read-bookmark-reply',
  
  CLIPBOARD_WRITE_BOOKMARK: 'clipboard:write-bookmark',
  CLIPBOARD_WRITE_BOOKMARK_REPLY: 'clipboard:write-bookmark-reply',
  
  CLIPBOARD_READ_FIND_TEXT: 'clipboard:read-find-text',
  CLIPBOARD_READ_FIND_TEXT_REPLY: 'clipboard:read-find-text-reply',
  
  CLIPBOARD_WRITE_FIND_TEXT: 'clipboard:write-find-text',
  CLIPBOARD_WRITE_FIND_TEXT_REPLY: 'clipboard:write-find-text-reply',
  
  CLIPBOARD_CLEAR: 'clipboard:clear',
  CLIPBOARD_CLEAR_REPLY: 'clipboard:clear-reply',
  
  // Notification related channels
  NOTIFICATION_CREATE: 'notification:create',
  NOTIFICATION_CREATE_REPLY: 'notification:create-reply',
  
  NOTIFICATION_IS_SUPPORTED: 'notification:is-supported',
  NOTIFICATION_IS_SUPPORTED_REPLY: 'notification:is-supported-reply',
  
  NOTIFICATION_GET_PERMISSION_LEVEL: 'notification:get-permission-level',
  NOTIFICATION_GET_PERMISSION_LEVEL_REPLY: 'notification:get-permission-level-reply',
  
  NOTIFICATION_REQUEST_PERMISSION: 'notification:request-permission',
  NOTIFICATION_REQUEST_PERMISSION_REPLY: 'notification:request-permission-reply',
  
  NOTIFICATION_SHOW: 'notification:show',
  NOTIFICATION_SHOW_REPLY: 'notification:show-reply',
  
  NOTIFICATION_CLOSE: 'notification:close',
  NOTIFICATION_CLOSE_REPLY: 'notification:close-reply',
  
  NOTIFICATION_ON_CLICK: 'notification:on-click',
  NOTIFICATION_ON_CLICK_REPLY: 'notification:on-click-reply',
  
  NOTIFICATION_ON_CLOSE: 'notification:on-close',
  NOTIFICATION_ON_CLOSE_REPLY: 'notification:on-close-reply',
  
  NOTIFICATION_ON_SHOW: 'notification:on-show',
  NOTIFICATION_ON_SHOW_REPLY: 'notification:on-show-reply',
  
  NOTIFICATION_ON_ACTION: 'notification:on-action',
  NOTIFICATION_ON_ACTION_REPLY: 'notification:on-action-reply',
  
  NOTIFICATION_ON_FAILED: 'notification:on-failed',
  NOTIFICATION_ON_FAILED_REPLY: 'notification:on-failed-reply',
  
  // Game related channels
  GAME_GET_ALL: 'game:get-all',
  GAME_GET_ALL_REPLY: 'game:get-all-reply',
  
  GAME_GET_BY_ID: 'game:get-by-id',
  GAME_GET_BY_ID_REPLY: 'game:get-by-id-reply',
  
  GAME_INSTALL: 'game:install',
  GAME_INSTALL_REPLY: 'game:install-reply',
  
  GAME_UNINSTALL: 'game:uninstall',
  GAME_UNINSTALL_REPLY: 'game:uninstall-reply',
  
  GAME_LAUNCH: 'game:launch',
  GAME_LAUNCH_REPLY: 'game:launch-reply',
  
  GAME_GET_STATUS: 'game:get-status',
  GAME_GET_STATUS_REPLY: 'game:get-status-reply',
  
  GAME_CHECK_FOR_UPDATES: 'game:check-for-updates',
  GAME_CHECK_FOR_UPDATES_REPLY: 'game:check-for-updates-reply',
  
  GAME_UPDATE: 'game:update',
  GAME_UPDATE_REPLY: 'game:update-reply',
  
  // Authentication related channels
  AUTH_LOGIN: 'auth:login',
  AUTH_LOGIN_REPLY: 'auth:login-reply',

  AUTH_REGISTER: 'auth:register',
  AUTH_REGISTER_REPLY: 'auth:register-reply',

  AUTH_LOGOUT: 'auth:logout',
  AUTH_LOGOUT_REPLY: 'auth:logout-reply',

  AUTH_VALIDATE_TOKEN: 'auth:validate-token',
  AUTH_VALIDATE_TOKEN_REPLY: 'auth:validate-token-reply',

  AUTH_REFRESH_TOKEN: 'auth:refresh-token',
  AUTH_REFRESH_TOKEN_REPLY: 'auth:refresh-token-reply',

  // User related channels
  USER_GET: 'user:get',
  USER_GET_REPLY: 'user:get-reply',

  USER_UPDATE: 'user:update',
  USER_UPDATE_REPLY: 'user:update-reply',

  USER_DELETE: 'user:delete',
  USER_DELETE_REPLY: 'user:delete-reply',

  // Settings related channels
  SETTINGS_GET: 'settings:get',
  SETTINGS_GET_REPLY: 'settings:get-reply',

  SETTINGS_SET: 'settings:set',
  SETTINGS_SET_REPLY: 'settings:set-reply',

  SETTINGS_UPDATE: 'settings:update',
  SETTINGS_UPDATE_REPLY: 'settings:update-reply',

  SETTINGS_RESET: 'settings:reset',
  SETTINGS_RESET_REPLY: 'settings:reset-reply',
  
  // Update related channels
  UPDATE_CHECK: 'update:check',
  UPDATE_CHECK_REPLY: 'update:check-reply',

  UPDATE_DOWNLOAD: 'update:download',
  UPDATE_DOWNLOAD_REPLY: 'update:download-reply',

  UPDATE_INSTALL: 'update:install',
  UPDATE_INSTALL_REPLY: 'update:install-reply',

  UPDATE_GET_STATUS: 'update:get-status',
  UPDATE_GET_STATUS_REPLY: 'update:get-status-reply',
  
  // System information channels
  SYSTEM_GET_INFO: 'system:get-info',
  SYSTEM_GET_INFO_REPLY: 'system:get-info-reply',
  
  SYSTEM_GET_USAGE: 'system:get-usage',
  SYSTEM_GET_USAGE_REPLY: 'system:get-usage-reply',
  
  // Analytics channels
  ANALYTICS_TRACK: 'analytics:track',
  ANALYTICS_TRACK_REPLY: 'analytics:track-reply',
  
  ANALYTICS_PAGE_VIEW: 'analytics:page-view',
  ANALYTICS_PAGE_VIEW_REPLY: 'analytics:page-view-reply',
  
  // Error reporting channels
  ERROR_REPORT: 'error:report',
  ERROR_REPORT_REPLY: 'error:report-reply',
  
  // Logging channels
  LOG: 'log',
  LOG_REPLY: 'log-reply',
  
  // Generic message channels
  MESSAGE: 'message',
  MESSAGE_REPLY: 'message-reply',
} as const;

// Type for IPC channel names
export type IpcChannel = keyof typeof IPC_CHANNELS;

// Type for IPC channel values
export type IpcChannelValue = typeof IPC_CHANNELS[IpcChannel];

// Type for IPC message
export interface IpcMessage<T = any> {
  channel: IpcChannelValue;
  data?: T;
  id?: string;
  error?: Error;
}

// Type for IPC handler
export type IpcHandler<T = any, R = any> = (
  event: Electron.IpcMainInvokeEvent,
  data: T
) => Promise<R> | R;

// Type for IPC event handler
export type IpcEventHandler<T = any> = (
  event: Electron.IpcMainEvent,
  data: T
) => void;

// Type for IPC reply handler
export type IpcReplyHandler<T = any> = (
  event: Electron.IpcRendererEvent,
  data: T
) => void;

// Type for IPC reply
export interface IpcReply<T = any> {
  data?: T;
  error?: Error;
}
