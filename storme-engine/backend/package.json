{"name": "storme-engine-backend", "version": "1.0.0", "description": "Backend API for Storme Engine Desktop App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["storme", "game", "launcher", "api", "backend", "electron"], "author": "GameStorme Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "uuid": "^9.0.1", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}