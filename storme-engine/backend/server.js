const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// In-memory storage (replace with database in production)
const users = new Map();
const games = new Map();
const userSessions = new Map();
const gameStats = new Map();

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'storme-engine-secret-key-2025';

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:8000', 'http://localhost:3000', 'app://storme-engine'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000 // limit each IP to 1000 requests per windowMs
});
app.use('/api/', limiter);

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'Storme Engine Backend is running!', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Authentication endpoints
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, displayName, userType = 'gamer' } = req.body;

    if (!email || !password || !displayName) {
      return res.status(400).json({ error: 'Email, password, and display name are required' });
    }

    // Check if user already exists
    const existingUser = Array.from(users.values()).find(user => user.email === email);
    if (existingUser) {
      return res.status(409).json({ error: 'User already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const userId = uuidv4();
    const user = {
      id: userId,
      email,
      displayName,
      userType,
      password: hashedPassword,
      createdAt: new Date().toISOString(),
      lastLogin: null,
      profile: {
        avatar: null,
        level: 1,
        experience: 0,
        gamesPlayed: 0,
        totalPlaytime: 0,
        achievements: []
      }
    };

    users.set(userId, user);

    // Generate JWT token
    const token = jwt.sign(
      { userId, email, userType },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Remove password from response
    const { password: _, ...userResponse } = user;

    res.status(201).json({
      success: true,
      user: userResponse,
      token,
      message: 'User registered successfully'
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // Find user
    const user = Array.from(users.values()).find(user => user.email === email);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Update last login
    user.lastLogin = new Date().toISOString();
    users.set(user.id, user);

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email, userType: user.userType },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Remove password from response
    const { password: _, ...userResponse } = user;

    res.json({
      success: true,
      user: userResponse,
      token,
      message: 'Login successful'
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

app.post('/api/auth/logout', authenticateToken, (req, res) => {
  // In a real app, you'd invalidate the token in a blacklist
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

app.get('/api/auth/me', authenticateToken, (req, res) => {
  const user = users.get(req.user.userId);
  if (!user) {
    return res.status(404).json({ error: 'User not found' });
  }

  const { password: _, ...userResponse } = user;
  res.json({
    success: true,
    user: userResponse
  });
});

// Initialize sample data
function initializeSampleData() {
  // Sample games
  const sampleGames = [
    {
      id: 'cyber-legends',
      title: 'Cyber Legends',
      description: 'An epic cyberpunk adventure in a neon-lit future city.',
      image: '/api/placeholder/300/200',
      genre: ['Action', 'RPG'],
      rating: 4.8,
      price: 29.99,
      isFree: false,
      isInstalled: false,
      isFavorite: false,
      developer: 'CyberStudio',
      releaseDate: '2024-01-15',
      size: '2.5 GB',
      version: '1.2.0',
      platforms: ['Windows', 'macOS', 'Linux'],
      screenshots: ['/api/placeholder/800/450', '/api/placeholder/800/450'],
      systemRequirements: {
        minimum: {
          os: 'macOS 10.15+',
          processor: 'Intel Core i5 or Apple M1',
          memory: '8 GB RAM',
          graphics: 'Metal-compatible GPU',
          storage: '3 GB available space'
        }
      }
    },
    {
      id: 'space-explorer',
      title: 'Space Explorer',
      description: 'Explore the vast universe and discover new worlds.',
      image: '/api/placeholder/300/200',
      genre: ['Adventure', 'Simulation'],
      rating: 4.6,
      price: 0,
      isFree: true,
      isInstalled: true,
      isFavorite: true,
      developer: 'Cosmic Games',
      releaseDate: '2023-11-20',
      size: '1.8 GB',
      version: '2.1.3',
      platforms: ['Windows', 'macOS'],
      screenshots: ['/api/placeholder/800/450', '/api/placeholder/800/450'],
      systemRequirements: {
        minimum: {
          os: 'macOS 10.14+',
          processor: 'Intel Core i3 or Apple M1',
          memory: '4 GB RAM',
          graphics: 'Metal-compatible GPU',
          storage: '2 GB available space'
        }
      }
    },
    {
      id: 'medieval-kingdoms',
      title: 'Medieval Kingdoms',
      description: 'Build your empire in this strategic medieval game.',
      image: '/api/placeholder/300/200',
      genre: ['Strategy', 'Simulation'],
      rating: 4.7,
      price: 39.99,
      isFree: false,
      isInstalled: true,
      isFavorite: false,
      developer: 'Kingdom Studios',
      releaseDate: '2024-03-10',
      size: '4.2 GB',
      version: '1.0.5',
      platforms: ['Windows', 'macOS', 'Linux'],
      screenshots: ['/api/placeholder/800/450', '/api/placeholder/800/450'],
      systemRequirements: {
        minimum: {
          os: 'macOS 11.0+',
          processor: 'Apple M1 or Intel Core i5',
          memory: '8 GB RAM',
          graphics: 'Metal-compatible GPU',
          storage: '5 GB available space'
        }
      }
    },
    {
      id: 'racing-thunder',
      title: 'Racing Thunder',
      description: 'High-speed racing with stunning graphics.',
      image: '/api/placeholder/300/200',
      genre: ['Racing', 'Sports'],
      rating: 4.5,
      price: 19.99,
      isFree: false,
      isInstalled: false,
      isFavorite: true,
      developer: 'Speed Demons',
      releaseDate: '2024-02-28',
      size: '3.1 GB',
      version: '1.1.2',
      platforms: ['Windows', 'macOS'],
      screenshots: ['/api/placeholder/800/450', '/api/placeholder/800/450'],
      systemRequirements: {
        minimum: {
          os: 'macOS 10.15+',
          processor: 'Apple M1 or Intel Core i7',
          memory: '12 GB RAM',
          graphics: 'Metal-compatible GPU with 4GB VRAM',
          storage: '4 GB available space'
        }
      }
    }
  ];

  sampleGames.forEach(game => {
    games.set(game.id, game);
  });

  console.log(`✅ Initialized ${sampleGames.length} sample games`);
}

// Games endpoints
app.get('/api/games', (req, res) => {
  try {
    const { search, genre, platform, status, limit = 20, page = 1 } = req.query;
    let gamesList = Array.from(games.values());

    // Apply filters
    if (search) {
      const searchLower = search.toLowerCase();
      gamesList = gamesList.filter(game =>
        game.title.toLowerCase().includes(searchLower) ||
        game.description.toLowerCase().includes(searchLower) ||
        game.developer.toLowerCase().includes(searchLower)
      );
    }

    if (genre) {
      gamesList = gamesList.filter(game =>
        game.genre.some(g => g.toLowerCase() === genre.toLowerCase())
      );
    }

    if (platform) {
      gamesList = gamesList.filter(game =>
        game.platforms.some(p => p.toLowerCase() === platform.toLowerCase())
      );
    }

    if (status === 'installed') {
      gamesList = gamesList.filter(game => game.isInstalled);
    } else if (status === 'favorites') {
      gamesList = gamesList.filter(game => game.isFavorite);
    } else if (status === 'free') {
      gamesList = gamesList.filter(game => game.isFree);
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedGames = gamesList.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedGames,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: gamesList.length,
        totalPages: Math.ceil(gamesList.length / limitNum),
        hasNext: endIndex < gamesList.length,
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Games fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch games' });
  }
});

app.get('/api/games/:id', (req, res) => {
  try {
    const { id } = req.params;
    const game = games.get(id);

    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }

    res.json({
      success: true,
      data: game
    });
  } catch (error) {
    console.error('Game fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch game' });
  }
});

app.post('/api/games/:id/install', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const game = games.get(id);

    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }

    // Simulate installation process
    game.isInstalled = true;
    games.set(id, game);

    // Update user stats
    const user = users.get(req.user.userId);
    if (user) {
      user.profile.gamesPlayed += 1;
      users.set(req.user.userId, user);
    }

    res.json({
      success: true,
      message: 'Game installed successfully',
      data: game
    });
  } catch (error) {
    console.error('Game install error:', error);
    res.status(500).json({ error: 'Failed to install game' });
  }
});

app.post('/api/games/:id/uninstall', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const game = games.get(id);

    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }

    game.isInstalled = false;
    games.set(id, game);

    res.json({
      success: true,
      message: 'Game uninstalled successfully',
      data: game
    });
  } catch (error) {
    console.error('Game uninstall error:', error);
    res.status(500).json({ error: 'Failed to uninstall game' });
  }
});

app.post('/api/games/:id/favorite', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const game = games.get(id);

    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }

    game.isFavorite = !game.isFavorite;
    games.set(id, game);

    res.json({
      success: true,
      message: game.isFavorite ? 'Added to favorites' : 'Removed from favorites',
      data: game
    });
  } catch (error) {
    console.error('Game favorite error:', error);
    res.status(500).json({ error: 'Failed to update favorite status' });
  }
});

// User dashboard endpoints
app.get('/api/user/dashboard', authenticateToken, (req, res) => {
  try {
    const user = users.get(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userGames = Array.from(games.values()).filter(game => game.isInstalled);
    const favoriteGames = Array.from(games.values()).filter(game => game.isFavorite);
    const recentGames = userGames.slice(0, 3).map(game => ({
      ...game,
      lastPlayed: '2 hours ago',
      playtime: `${Math.floor(Math.random() * 50) + 5}h ${Math.floor(Math.random() * 60)}m`
    }));

    const dashboardData = {
      user: {
        ...user,
        password: undefined
      },
      stats: {
        gamesPlayed: userGames.length,
        totalPlaytime: `${Math.floor(Math.random() * 200) + 50}h ${Math.floor(Math.random() * 60)}m`,
        achievements: Math.floor(Math.random() * 100) + 20,
        level: user.profile.level,
        experience: user.profile.experience,
        maxExperience: user.profile.level * 1000
      },
      recentGames,
      favoriteGames: favoriteGames.slice(0, 5),
      recommendations: Array.from(games.values()).slice(0, 4)
    };

    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('Dashboard fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard data' });
  }
});

app.get('/api/user/stats', authenticateToken, (req, res) => {
  try {
    const user = users.get(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userGames = Array.from(games.values()).filter(game => game.isInstalled);

    const stats = {
      gamesPlayed: userGames.length,
      totalPlaytime: `${Math.floor(Math.random() * 200) + 50}h ${Math.floor(Math.random() * 60)}m`,
      achievements: Math.floor(Math.random() * 100) + 20,
      level: user.profile.level,
      experience: user.profile.experience,
      maxExperience: user.profile.level * 1000,
      favoriteGenres: ['Action', 'RPG', 'Strategy'],
      averageRating: 4.6,
      totalDownloads: userGames.length,
      weeklyPlaytime: `${Math.floor(Math.random() * 30) + 10}h`
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Stats fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch user stats' });
  }
});

// Developer dashboard endpoints
app.get('/api/developer/dashboard', authenticateToken, (req, res) => {
  try {
    const user = users.get(req.user.userId);
    if (!user || user.userType !== 'developer') {
      return res.status(403).json({ error: 'Developer access required' });
    }

    const developerStats = {
      totalGames: 5,
      totalDownloads: 12500,
      revenue: 8750.50,
      activeUsers: 3200,
      monthlyRevenue: 2100.25,
      averageRating: 4.6,
      totalReviews: 1250,
      conversionRate: 3.2
    };

    const recentProjects = [
      {
        id: 'project-1',
        name: 'Cyber Adventure',
        status: 'Published',
        downloads: 5200,
        revenue: 3200.50,
        lastUpdate: '2024-01-15'
      },
      {
        id: 'project-2',
        name: 'Space Quest',
        status: 'In Review',
        downloads: 0,
        revenue: 0,
        lastUpdate: '2024-01-20'
      }
    ];

    res.json({
      success: true,
      data: {
        user: {
          ...user,
          password: undefined
        },
        stats: developerStats,
        recentProjects,
        analytics: {
          dailyDownloads: Array.from({length: 7}, () => Math.floor(Math.random() * 100) + 20),
          dailyRevenue: Array.from({length: 7}, () => Math.floor(Math.random() * 200) + 50)
        }
      }
    });
  } catch (error) {
    console.error('Developer dashboard error:', error);
    res.status(500).json({ error: 'Failed to fetch developer dashboard' });
  }
});

// Initialize sample data on startup
initializeSampleData();

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Storme Engine Backend running on port ${PORT}`);
  console.log(`🎮 Game Library: ${games.size} games loaded`);
  console.log(`👥 User System: Ready for authentication`);
  console.log(`📊 Dashboard APIs: Gamer & Developer ready`);
  console.log(`🔒 Security: JWT authentication enabled`);
});

module.exports = app;
