# Storme Engine Backend Configuration

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=storme-engine-secret-key-2025-change-in-production
JWT_EXPIRES_IN=7d

# Database Configuration (if using external database)
# DATABASE_URL=postgresql://username:password@localhost:5432/storme_engine
# REDIS_URL=redis://localhost:6379

# Firebase Configuration (optional)
FIREBASE_PROJECT_ID=gamestorme-faf42
FIREBASE_CLIENT_EMAIL=
FIREBASE_PRIVATE_KEY=
FIREBASE_DATABASE_URL=https://gamestorme-faf42-default-rtdb.firebaseio.com
FIREBASE_STORAGE_BUCKET=gamestorme-faf42.firebasestorage.app

# External APIs
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:8000,http://localhost:3000,app://storme-engine

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/storme-engine.log
