{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "dist/preload", "rootDir": "src/main/preload", "sourceMap": true, "declaration": false, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true}, "include": ["src/main/preload/**/*.ts"], "exclude": ["node_modules"]}