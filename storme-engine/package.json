{"name": "storme-engine", "productName": "Storme Engine", "version": "1.0.0", "description": "Storme Engine - Desktop Game Launcher by Gamestorme", "main": "dist/main/index.js", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "copyright": "Copyright 2025 <PERSON>", "scripts": {"start": "electron .", "dev": "concurrently \"vite\" \"electron .\"", "build": "vite build && tsc -p tsconfig.main.json && tsc -p tsconfig.preload.json && tsc -p tsconfig.renderer.json", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "postinstall": "electron-builder install-app-deps", "publish": "electron-builder -p always"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.5", "@tanstack/react-query": "^5.13.3", "@tanstack/react-query-devtools": "^5.13.3", "custom-electron-titlebar": "^4.1.0", "electron-log": "^5.0.0", "electron-squirrel-startup": "1.0.1", "electron-store": "^8.1.0", "electron-updater": "^5.3.0", "i18next": "^23.7.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.1", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.0", "styled-components": "^6.1.8", "zustand": "^4.5.0"}, "devDependencies": {"@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@types/electron": "1.4.38", "@types/electron-is-dev": "0.3.0", "@types/i18next-browser-languagedetector": "2.0.2", "@types/node": "18.19.115", "@types/react": "18.3.23", "@types/react-dom": "18.3.7", "@types/react-helmet": "6.1.11", "@types/react-i18next": "7.8.3", "@types/react-router-dom": "5.3.3", "@types/styled-components": "5.1.34", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-react": "3.1.0", "concurrently": "^8.0.0", "electron": "25.9.8", "electron-builder": "24.13.3", "electron-devtools-installer": "4.0.0", "electron-is-dev": "3.0.1", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.0.0", "vite": "^4.3.0", "vite-tsconfig-paths": "^4.0.5"}, "build": {"appId": "com.gamestorme.storme", "productName": "Storme Engine", "copyright": "Copyright © 2025 GameStorme", "files": ["dist/**/*", "node_modules/**/*", "!node_modules/**/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/**/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/.cache", "!node_modules/.bin"], "directories": {"buildResources": "build", "output": "release/${version}"}, "extraResources": [{"from": "src/assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build/icons/icon.ico", "publisherName": "GameStorme", "fileAssociations": [{"ext": "gsgame", "name": "<PERSON>e Game", "description": "Storme Game Package", "role": "Editor"}]}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build/icons/icon.icns", "category": "public.app-category.games", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "extendInfo": {"NSCameraUsageDescription": "The app needs access to the camera for some features", "NSMicrophoneUsageDescription": "The app needs access to the microphone for voice chat", "CFBundleDocumentTypes": [{"CFBundleTypeName": "<PERSON>e Game", "CFBundleTypeExtensions": ["gsgame"], "CFBundleTypeRole": "Editor"}]}}, "linux": {"target": ["AppImage", "deb"], "icon": "build/icons/icon.png", "category": "Game", "maintainer": "<EMAIL>", "vendor": "GameStorme", "synopsis": "Storme Game Engine", "description": "A modern game launcher and platform", "desktop": {"Name": "Storme Engine", "Comment": "Launch and manage your games", "Categories": "Game;", "Keywords": "game;launcher;storme;gamestorme;", "MimeType": "application/x-storme-game;"}}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Storme Engine", "uninstallDisplayName": "Storme Engine", "include": "build/installer.nsh", "license": "LICENSE", "artifactName": "${productName}-Setup-${version}.${ext}"}, "publish": {"provider": "github", "repo": "storme-engine", "owner": "gamestorme", "private": false, "releaseType": "draft"}}}