#!/bin/bash

# Test Build Script for Storme Engine
echo "🧪 Testing Storme Engine Build Process..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm install --silent

echo "🔨 Testing Vite build..."
npx vite build --mode development

if [ $? -eq 0 ]; then
    echo "✅ Vite build successful"
else
    echo "❌ Vite build failed"
    exit 1
fi

echo "🎮 Testing Electron packaging..."
# Test if electron can be imported
node -e "
try {
  require('electron');
  console.log('✅ Electron is available');
} catch (e) {
  console.log('❌ Electron not found:', e.message);
  process.exit(1);
}
"

echo "📱 Testing API service..."
node -e "
const fs = require('fs');
const path = './src/renderer/src/services/api.ts';
if (fs.existsSync(path)) {
  console.log('✅ API service exists');
} else {
  console.log('❌ API service not found');
  process.exit(1);
}
"

echo "🎯 Testing component structure..."
node -e "
const fs = require('fs');
const components = [
  './src/renderer/src/pages/GamesPage.tsx',
  './src/renderer/src/pages/DashboardPage.tsx',
  './src/renderer/src/components/common/GameLoadingScreen.tsx'
];

let allExist = true;
components.forEach(comp => {
  if (fs.existsSync(comp)) {
    console.log('✅', comp.split('/').pop(), 'exists');
  } else {
    console.log('❌', comp.split('/').pop(), 'missing');
    allExist = false;
  }
});

if (!allExist) process.exit(1);
"

echo ""
echo "🎉 Build test completed successfully!"
echo ""
echo "📋 Test Summary:"
echo "   ✅ Dependencies installed"
echo "   ✅ Vite build working"
echo "   ✅ Electron available"
echo "   ✅ API service ready"
echo "   ✅ Components structure valid"
echo ""
echo "🚀 Ready for production build!"
echo "   Run: npm run dist:mac (for macOS)"
echo "   Run: ./build.sh (automated build)"
