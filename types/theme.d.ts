import { Theme, ThemeOptions } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface PaletteColor {
    glow?: string;
  }

  interface SimplePaletteColorOptions {
    glow?: string;
  }

  interface TypeBackground {
    glass?: string;
    glassHover?: string;
    mesh?: string;
  }

  interface Palette {
    background: TypeBackground;
  }

  interface PaletteOptions {
    background?: Partial<TypeBackground>;
  }
}

export {};
