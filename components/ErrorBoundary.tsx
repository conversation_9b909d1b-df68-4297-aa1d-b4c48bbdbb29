import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Alert, Container } from '@mui/material';
import { styled } from '@mui/material/styles';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

const ErrorContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, ${theme.palette.error.dark}15 0%, ${theme.palette.error.light}15 100%)`,
}));

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 Error Boundary caught an error:', error, errorInfo);
    
    // Log error to analytics or error reporting service
    if (typeof window !== 'undefined') {
      // You can add error reporting here
      console.error('Error details:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      });
    }

    this.setState({
      error,
      errorInfo
    });
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorContainer>
          <Container maxWidth="md">
            <Box textAlign="center">
              <Typography variant="h1" sx={{ fontSize: '4rem', mb: 2 }}>
                🚨
              </Typography>
              <Typography variant="h4" gutterBottom>
                Oops! Something went wrong
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                We encountered an unexpected error. Our team has been notified and is working on a fix.
              </Typography>
              
              <Alert severity="error" sx={{ mb: 4, textAlign: 'left' }}>
                <Typography variant="h6" gutterBottom>
                  Error Details:
                </Typography>
                <Typography variant="body2" component="pre" sx={{ 
                  whiteSpace: 'pre-wrap', 
                  wordBreak: 'break-word',
                  maxHeight: 200,
                  overflow: 'auto',
                  fontSize: '0.8rem'
                }}>
                  {this.state.error?.message || 'Unknown error occurred'}
                </Typography>
                {process.env.NODE_ENV === 'development' && this.state.error?.stack && (
                  <details style={{ marginTop: 16 }}>
                    <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                      Stack Trace (Development)
                    </summary>
                    <Typography variant="body2" component="pre" sx={{ 
                      whiteSpace: 'pre-wrap', 
                      wordBreak: 'break-word',
                      fontSize: '0.7rem',
                      mt: 1,
                      p: 1,
                      bgcolor: 'rgba(0,0,0,0.1)',
                      borderRadius: 1,
                      maxHeight: 300,
                      overflow: 'auto'
                    }}>
                      {this.state.error.stack}
                    </Typography>
                  </details>
                )}
              </Alert>

              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={this.handleReload}
                  size="large"
                >
                  Reload Page
                </Button>
                <Button 
                  variant="outlined" 
                  onClick={this.handleGoHome}
                  size="large"
                >
                  Go to Homepage
                </Button>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mt: 4 }}>
                If this problem persists, please contact our support team at{' '}
                <a href="mailto:<EMAIL>" style={{ color: 'inherit' }}>
                  <EMAIL>
                </a>
              </Typography>
            </Box>
          </Container>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
